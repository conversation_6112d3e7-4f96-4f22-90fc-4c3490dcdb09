<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المبيعات - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow d-print-none">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>

<div class="container">
  <!-- عنوان التقرير -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="bi bi-graph-up"></i> تقرير المبيعات</h3>
    <div class="d-print-none">
      <button onclick="window.print()" class="btn btn-primary me-2">
        <i class="bi bi-printer"></i> طباعة
      </button>
      <a href="/reports" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> العودة
      </a>
    </div>
  </div>
  
  <!-- فلتر التواريخ -->
  <div class="card shadow mb-4 d-print-none">
    <div class="card-body">
      <form method="get" class="row">
        <div class="col-md-4 mb-2">
          <label class="form-label">من تاريخ</label>
          <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
        </div>
        <div class="col-md-4 mb-2">
          <label class="form-label">إلى تاريخ</label>
          <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
        </div>
        <div class="col-md-4 mb-2 d-flex align-items-end">
          <button type="submit" class="btn btn-primary w-100">
            <i class="bi bi-search"></i> تطبيق الفلتر
          </button>
        </div>
      </form>
    </div>
  </div>
  
  <!-- ملخص الإحصائيات -->
  <div class="row mb-4">
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card">
        <div class="card-body">
          <i class="bi bi-currency-dollar display-4 mb-3"></i>
          <h5 class="card-title">إجمالي المبيعات</h5>
          <p class="display-6 fw-bold">{{ "{:,.2f}".format(total_sales) }}</p>
          <small>جنيه مصري</small>
        </div>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card-2">
        <div class="card-body">
          <i class="bi bi-receipt display-4 mb-3"></i>
          <h5 class="card-title">عدد الفواتير</h5>
          <p class="display-6 fw-bold">{{ total_invoices }}</p>
          <small>فاتورة</small>
        </div>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card-3">
        <div class="card-body">
          <i class="bi bi-calculator display-4 mb-3"></i>
          <h5 class="card-title">متوسط الفاتورة</h5>
          <p class="display-6 fw-bold">{{ "{:,.2f}".format(avg_invoice) }}</p>
          <small>جنيه مصري</small>
        </div>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card">
        <div class="card-body">
          <i class="bi bi-calendar-range display-4 mb-3"></i>
          <h5 class="card-title">الفترة</h5>
          <p class="fs-6 fw-bold">{{ start_date }}</p>
          <p class="fs-6 fw-bold">{{ end_date }}</p>
        </div>
      </div>
    </div>
  </div>
  
  <!-- أكثر المنتجات مبيعاً -->
  {% if top_products %}
  <div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
      <h5 class="mb-0"><i class="bi bi-trophy"></i> أكثر المنتجات مبيعاً</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>الترتيب</th>
              <th>اسم العطر</th>
              <th>الشركة</th>
              <th>الكمية المباعة</th>
              <th>إجمالي المبيعات</th>
            </tr>
          </thead>
          <tbody>
            {% for product in top_products %}
            <tr>
              <td>
                <span class="badge bg-{% if loop.index == 1 %}warning{% elif loop.index == 2 %}secondary{% elif loop.index == 3 %}dark{% else %}light text-dark{% endif %}">
                  #{{ loop.index }}
                </span>
              </td>
              <td><strong>{{ product.name }}</strong></td>
              <td>{{ product.company }}</td>
              <td>
                <span class="badge bg-primary">{{ product.total_quantity }}</span>
              </td>
              <td>
                <span class="text-success fw-bold">{{ "{:,.2f}".format(product.total_sales) }} ج.م</span>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
  {% endif %}
  
  <!-- رسم بياني للمبيعات -->
  <div class="card shadow mb-4 d-print-none">
    <div class="card-header bg-info text-white">
      <h5 class="mb-0"><i class="bi bi-bar-chart"></i> الرسم البياني للمبيعات</h5>
    </div>
    <div class="card-body">
      <canvas id="salesChart" width="400" height="200"></canvas>
    </div>
  </div>
  
  <!-- تفاصيل الفواتير -->
  <div class="card shadow">
    <div class="card-header bg-secondary text-white">
      <h5 class="mb-0"><i class="bi bi-list"></i> تفاصيل الفواتير</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>رقم الفاتورة</th>
              <th>التاريخ</th>
              <th>الوقت</th>
              <th>الإجمالي</th>
              <th class="d-print-none">إجراءات</th>
            </tr>
          </thead>
          <tbody>
            {% for invoice in invoices %}
            <tr>
              <td><strong>#{{ invoice.id }}</strong></td>
              <td>{{ invoice.date.strftime('%Y-%m-%d') }}</td>
              <td>{{ invoice.date.strftime('%H:%M') }}</td>
              <td>
                <span class="text-success fw-bold">{{ "{:,.2f}".format(invoice.total) }} ج.م</span>
              </td>
              <td class="d-print-none">
                <a href="/invoices/{{ invoice.id }}" class="btn btn-sm btn-info">
                  <i class="bi bi-eye"></i> عرض
                </a>
              </td>
            </tr>
            {% else %}
            <tr>
              <td colspan="5" class="text-center text-muted py-4">
                <i class="bi bi-receipt display-6 d-block mb-2"></i>
                لا توجد فواتير في هذه الفترة
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
<script>
// رسم بياني للمبيعات
const ctx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [{% for invoice in invoices %}'{{ invoice.date.strftime("%m-%d") }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'المبيعات اليومية',
            data: [{% for invoice in invoices %}{{ invoice.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'تطور المبيعات خلال الفترة المحددة'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString('ar-EG') + ' ج.م';
                    }
                }
            }
        }
    }
});
</script>
</body>
</html>
