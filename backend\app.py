import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>anager, login_user, logout_user, login_required, current_user
from datetime import datetime
from models import db, User, Perfume, Invoice, InvoiceItem, Supplier, Category, Customer, StockMovement, Role
from functools import wraps
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
import io
import pandas as pd

app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, '../data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Decorators للتحقق من الصلاحيات
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def manager_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_manager():
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated or not current_user.has_permission(permission):
                flash('ليس لديك صلاحية لتنفيذ هذا الإجراء', 'danger')
                return redirect(url_for('dashboard'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            if user.is_active:
                # تحديث آخر تسجيل دخول
                from datetime import datetime
                user.last_login = datetime.now()
                db.session.commit()
                login_user(user)
                return redirect(url_for('dashboard'))
            else:
                flash('حسابك معطل، يرجى التواصل مع المدير', 'danger')
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/')
@login_required
def dashboard():
    perfumes_count = Perfume.query.count()
    invoices_count = Invoice.query.count()
    total_sales = db.session.query(db.func.sum(Invoice.total)).scalar() or 0
    # تم إلغاء تنبيهات المخزون المنخفض - النظام مفتوح
    # low_stock_perfumes = Perfume.query.filter(Perfume.quantity <= Perfume.min_quantity).all()
    low_stock_perfumes = []  # قائمة فارغة
    return render_template('dashboard.html', perfumes_count=perfumes_count, invoices_count=invoices_count, total_sales=total_sales, low_stock_perfumes=low_stock_perfumes)

@app.route('/products')
@login_required
def products():
    q = request.args.get('q', '').strip()
    if q:
        perfumes = Perfume.query.filter((Perfume.name.contains(q)) | (Perfume.company.contains(q))).all()
        search_query = q
    else:
        perfumes = Perfume.query.all()
        search_query = None

    # حساب عدد النتائج
    results_count = len(perfumes)

    return render_template('products.html', perfumes=perfumes, results_count=results_count, search_query=search_query)

@app.route('/products/add', methods=['GET', 'POST'])
@login_required
def add_product():
    if request.method == 'POST':
        name = request.form['name']
        company = request.form['company']
        min_quantity = int(request.form.get('min_quantity', 0))  # افتراضي 0
        sale_type = request.form['sale_type']

        # تحديد نوع البيع والأسعار
        if sale_type == 'weight':
            # بيع بالوزن
            price_per_kg = float(request.form['price_per_kg'])
            quantity_kg = float(request.form.get('quantity_kg', 999.999))  # كمية مفتوحة افتراضية
            quantity = int(quantity_kg * 1000)  # تحويل إلى جرامات
            price = price_per_kg  # السعر الأساسي هو سعر الكيلو

            perfume = Perfume(
                name=name,
                company=company,
                price=price,
                price_per_kg=price_per_kg,
                is_sold_by_weight=True,
                weight_unit='gram',
                quantity=quantity,
                min_quantity=min_quantity
            )
        else:
            # بيع بالقطعة
            price = float(request.form['price'])
            quantity = int(request.form.get('quantity', 999999))  # كمية مفتوحة افتراضية

            perfume = Perfume(
                name=name,
                company=company,
                price=price,
                price_per_kg=0,
                is_sold_by_weight=False,
                quantity=quantity,
                min_quantity=min_quantity
            )

        db.session.add(perfume)
        db.session.commit()
        flash('تمت إضافة العطر بنجاح', 'success')
        return redirect(url_for('products'))
    return render_template('product_form.html', title='إضافة عطر جديد', perfume=None)

@app.route('/products/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_product(id):
    perfume = Perfume.query.get_or_404(id)
    if request.method == 'POST':
        perfume.name = request.form['name']
        perfume.company = request.form['company']
        perfume.min_quantity = int(request.form.get('min_quantity', 0))  # افتراضي 0
        sale_type = request.form['sale_type']

        # تحديد نوع البيع والأسعار
        if sale_type == 'weight':
            # بيع بالوزن
            price_per_kg = float(request.form['price_per_kg'])
            quantity_kg = float(request.form.get('quantity_kg', 999.999))  # كمية مفتوحة افتراضية
            quantity = int(quantity_kg * 1000)  # تحويل إلى جرامات

            perfume.price = price_per_kg
            perfume.price_per_kg = price_per_kg
            perfume.is_sold_by_weight = True
            perfume.weight_unit = 'gram'
            perfume.quantity = quantity
        else:
            # بيع بالقطعة
            price = float(request.form['price'])
            quantity = int(request.form.get('quantity', 999999))  # كمية مفتوحة افتراضية

            perfume.price = price
            perfume.price_per_kg = 0
            perfume.is_sold_by_weight = False
            perfume.quantity = quantity

        db.session.commit()
        flash('تم تعديل بيانات العطر بنجاح', 'success')
        return redirect(url_for('products'))
    return render_template('product_form.html', title='تعديل عطر', perfume=perfume)

@app.route('/products/delete/<int:id>')
@login_required
def delete_product(id):
    perfume = Perfume.query.get_or_404(id)
    db.session.delete(perfume)
    db.session.commit()
    flash('تم حذف العطر بنجاح', 'success')
    return redirect(url_for('products'))

@app.route('/invoices/add', methods=['GET', 'POST'])
@login_required
def add_invoice():
    perfumes = Perfume.query.all()
    perfumes_data = [
        {
            'id': p.id,
            'name': p.name,
            'company': p.company,
            'price': p.price,
            'quantity': p.quantity,
            'min_quantity': p.min_quantity,
            'price_per_kg': p.price_per_kg,
            'is_sold_by_weight': p.is_sold_by_weight
        } for p in perfumes
    ]

    # حساب عدد العطور المتاحة
    total_perfumes_count = len(perfumes)
    if request.method == 'POST':
        try:
            items = request.form['items']
            import json
            items = json.loads(items)
            if not items:
                flash('يجب إضافة صنف واحد على الأقل', 'danger')
                return render_template('invoice_form.html', perfumes=perfumes, perfumes_data=perfumes_data, total_perfumes_count=total_perfumes_count)
            total = 0
            invoice_items = []
            for item in items:
                perfume = Perfume.query.get(item['id'])
                if not perfume:
                    flash(f'العطر غير موجود', 'danger')
                    return render_template('invoice_form.html', perfumes=perfumes, perfumes_data=perfumes_data, total_perfumes_count=total_perfumes_count)

                # التحقق من الكمية المتاحة (اختياري - يمكن تعطيله)
                required_quantity = item['quantity']
                # تم إلغاء قيود الكمية - النظام مفتوح للبيع بأي كمية
                # if perfume.quantity < required_quantity:
                #     if perfume.is_sold_by_weight:
                #         flash(f'الوزن المطلوب غير متوفر للعطر: {perfume.name} (متوفر: {perfume.quantity} جرام)', 'danger')
                #     else:
                #         flash(f'الكمية المطلوبة غير متوفرة للعطر: {perfume.name} (متوفر: {perfume.quantity} قطعة)', 'danger')
                #     return render_template('invoice_form.html', perfumes=perfumes, perfumes_data=perfumes_data)

                # حساب السعر
                if item.get('is_weight_based', False):
                    # بيع بالوزن
                    weight = item['weight']
                    unit_price = item['unit_price']
                    item_total = item['total_price']
                else:
                    # بيع بالقطعة
                    unit_price = perfume.price
                    item_total = unit_price * item['quantity']

                total += item_total
                invoice_items.append({
                    'perfume': perfume,
                    'quantity': item['quantity'],
                    'weight': item.get('weight', 0),
                    'is_weight_based': item.get('is_weight_based', False),
                    'unit_price': unit_price,
                    'price': item_total
                })
            invoice = Invoice(date=datetime.now(), total=total)
            db.session.add(invoice)
            db.session.flush()  # للحصول على id الفاتورة

            for item in invoice_items:
                perfume = item['perfume']
                quantity_to_deduct = item['quantity']

                # تقليل الكمية من المخزون
                perfume.quantity -= quantity_to_deduct

                # تسجيل حركة المخزون
                movement = StockMovement(
                    perfume_id=perfume.id,
                    movement_type='out',
                    quantity=-quantity_to_deduct,
                    reason='بيع',
                    reference_id=invoice.id,
                    reference_type='invoice',
                    created_by=current_user.id
                )
                db.session.add(movement)

                # إضافة عنصر الفاتورة
                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    perfume_id=perfume.id,
                    quantity=item['quantity'],
                    price=item['price'],
                    weight_in_grams=item.get('weight', 0),
                    is_weight_based=item.get('is_weight_based', False),
                    unit_price=item['unit_price']
                )
                db.session.add(invoice_item)
            db.session.commit()
            flash('تم حفظ الفاتورة بنجاح', 'success')
            return redirect(url_for('invoices'))
        except Exception as e:
            flash('حدث خطأ أثناء حفظ الفاتورة', 'danger')
            return render_template('invoice_form.html', perfumes=perfumes, perfumes_data=perfumes_data, total_perfumes_count=total_perfumes_count)
    return render_template('invoice_form.html', perfumes=perfumes, perfumes_data=perfumes_data, total_perfumes_count=total_perfumes_count)

@app.route('/invoices')
@login_required
def invoices():
    q = request.args.get('q', '').strip()
    if q:
        from sqlalchemy import or_, cast, String
        invoices = Invoice.query.filter(
            or_(
                cast(Invoice.id, String).contains(q),
                cast(Invoice.date, String).contains(q)
            )
        ).order_by(Invoice.date.desc()).all()
    else:
        invoices = Invoice.query.order_by(Invoice.date.desc()).all()
    return render_template('invoices.html', invoices=invoices)

@app.route('/invoices/<int:id>')
@login_required
def invoice_detail(id):
    invoice = Invoice.query.get_or_404(id)
    items = InvoiceItem.query.filter_by(invoice_id=invoice.id).all()
    return render_template('invoice_detail.html', invoice=invoice, items=items)

@app.route('/invoices/<int:id>/pdf')
@login_required
def invoice_pdf(id):
    invoice = Invoice.query.get_or_404(id)
    items = InvoiceItem.query.filter_by(invoice_id=invoice.id).all()
    buffer = io.BytesIO()
    p = canvas.Canvas(buffer, pagesize=A4)
    width, height = A4
    y = height - 50
    p.setFont("Helvetica-Bold", 16)
    p.drawString(200, y, "فاتورة بيع")
    y -= 30
    p.setFont("Helvetica", 12)
    p.drawString(50, y, f"رقم الفاتورة: {invoice.id}")
    p.drawString(350, y, f"التاريخ: {invoice.date.strftime('%Y-%m-%d %H:%M')}")
    y -= 30
    p.drawString(50, y, f"الإجمالي: {invoice.total} جنيه")
    y -= 40
    p.setFont("Helvetica-Bold", 12)
    p.drawString(50, y, "اسم العطر")
    p.drawString(180, y, "الشركة")
    p.drawString(300, y, "السعر")
    p.drawString(370, y, "الكمية")
    p.drawString(440, y, "الإجمالي")
    y -= 20
    p.setFont("Helvetica", 12)
    for item in items:
        p.drawString(50, y, str(item.perfume.name))
        p.drawString(180, y, str(item.perfume.company))
        p.drawString(300, y, str(item.price))
        p.drawString(370, y, str(item.quantity))
        p.drawString(440, y, str(item.price * item.quantity))
        y -= 20
        if y < 60:
            p.showPage()
            y = height - 50
    p.showPage()
    p.save()
    buffer.seek(0)
    return send_file(buffer, as_attachment=True, download_name=f'invoice_{invoice.id}.pdf', mimetype='application/pdf')

@app.route('/products/export')
@login_required
def export_products():
    perfumes = Perfume.query.all()
    data = [{
        'اسم العطر': p.name,
        'الشركة': p.company,
        'السعر': p.price,
        'الكمية المتاحة': p.quantity,
        'الحد الأدنى': p.min_quantity
    } for p in perfumes]
    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='العطور')
    output.seek(0)
    return send_file(output, as_attachment=True, download_name='perfumes.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@app.route('/invoices/export')
@login_required
def export_invoices():
    invoices = Invoice.query.order_by(Invoice.date.desc()).all()
    data = [{
        'رقم الفاتورة': inv.id,
        'التاريخ': inv.date.strftime('%Y-%m-%d %H:%M'),
        'الإجمالي': inv.total
    } for inv in invoices]
    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='الفواتير')
    output.seek(0)
    return send_file(output, as_attachment=True, download_name='invoices.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

# ===== إدارة الموردين =====
@app.route('/suppliers')
@login_required
def suppliers():
    q = request.args.get('q', '').strip()
    if q:
        suppliers = Supplier.query.filter(
            (Supplier.name.contains(q)) |
            (Supplier.contact_person.contains(q)) |
            (Supplier.phone.contains(q))
        ).all()
    else:
        suppliers = Supplier.query.all()
    return render_template('suppliers.html', suppliers=suppliers)

@app.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
def add_supplier():
    if request.method == 'POST':
        supplier = Supplier(
            name=request.form['name'],
            contact_person=request.form.get('contact_person'),
            phone=request.form.get('phone'),
            email=request.form.get('email'),
            address=request.form.get('address')
        )
        db.session.add(supplier)
        db.session.commit()
        flash('تمت إضافة المورد بنجاح', 'success')
        return redirect(url_for('suppliers'))
    return render_template('supplier_form.html', title='إضافة مورد جديد', supplier=None)

@app.route('/suppliers/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_supplier(id):
    supplier = Supplier.query.get_or_404(id)
    if request.method == 'POST':
        supplier.name = request.form['name']
        supplier.contact_person = request.form.get('contact_person')
        supplier.phone = request.form.get('phone')
        supplier.email = request.form.get('email')
        supplier.address = request.form.get('address')
        db.session.commit()
        flash('تم تعديل بيانات المورد بنجاح', 'success')
        return redirect(url_for('suppliers'))
    return render_template('supplier_form.html', title='تعديل مورد', supplier=supplier)

@app.route('/suppliers/delete/<int:id>')
@login_required
def delete_supplier(id):
    supplier = Supplier.query.get_or_404(id)
    # فحص إذا كان المورد مرتبط بمنتجات
    if supplier.perfumes:
        flash('لا يمكن حذف المورد لأنه مرتبط بمنتجات', 'danger')
    else:
        db.session.delete(supplier)
        db.session.commit()
        flash('تم حذف المورد بنجاح', 'success')
    return redirect(url_for('suppliers'))

# ===== إدارة العملاء =====
@app.route('/customers')
@login_required
def customers():
    q = request.args.get('q', '').strip()
    if q:
        customers = Customer.query.filter(
            (Customer.name.contains(q)) |
            (Customer.phone.contains(q)) |
            (Customer.email.contains(q))
        ).all()
    else:
        customers = Customer.query.all()
    return render_template('customers.html', customers=customers)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        customer = Customer(
            name=request.form['name'],
            phone=request.form.get('phone'),
            email=request.form.get('email'),
            address=request.form.get('address'),
            gender=request.form.get('gender'),
            notes=request.form.get('notes')
        )
        # تحويل تاريخ الميلاد
        birth_date = request.form.get('birth_date')
        if birth_date:
            from datetime import datetime
            customer.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()

        db.session.add(customer)
        db.session.commit()
        flash('تمت إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers'))
    return render_template('customer_form.html', title='إضافة عميل جديد', customer=None)

@app.route('/customers/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_customer(id):
    customer = Customer.query.get_or_404(id)
    if request.method == 'POST':
        customer.name = request.form['name']
        customer.phone = request.form.get('phone')
        customer.email = request.form.get('email')
        customer.address = request.form.get('address')
        customer.gender = request.form.get('gender')
        customer.notes = request.form.get('notes')

        # تحويل تاريخ الميلاد
        birth_date = request.form.get('birth_date')
        if birth_date:
            from datetime import datetime
            customer.birth_date = datetime.strptime(birth_date, '%Y-%m-%d').date()

        db.session.commit()
        flash('تم تعديل بيانات العميل بنجاح', 'success')
        return redirect(url_for('customers'))
    return render_template('customer_form.html', title='تعديل عميل', customer=customer)

@app.route('/customers/delete/<int:id>')
@login_required
def delete_customer(id):
    customer = Customer.query.get_or_404(id)
    # فحص إذا كان العميل له فواتير
    if customer.invoices:
        flash('لا يمكن حذف العميل لأنه له فواتير مسجلة', 'danger')
    else:
        db.session.delete(customer)
        db.session.commit()
        flash('تم حذف العميل بنجاح', 'success')
    return redirect(url_for('customers'))

# ===== إدارة المخزون =====
@app.route('/stock')
@login_required
def stock_management():
    # العطور منخفضة المخزون
    low_stock = Perfume.query.filter(Perfume.quantity <= Perfume.min_quantity).all()

    # العطور منتهية الصلاحية
    from datetime import date, timedelta
    today = date.today()
    expired = Perfume.query.filter(Perfume.expiry_date <= today).all()

    # العطور التي ستنتهي صلاحيتها خلال 30 يوم
    expiring_soon = Perfume.query.filter(
        Perfume.expiry_date > today,
        Perfume.expiry_date <= today + timedelta(days=30)
    ).all()

    # آخر حركات المخزون
    recent_movements = StockMovement.query.order_by(StockMovement.created_at.desc()).limit(10).all()

    return render_template('stock_management.html',
                         low_stock=low_stock,
                         expired=expired,
                         expiring_soon=expiring_soon,
                         recent_movements=recent_movements)

@app.route('/stock/movements')
@login_required
def stock_movements():
    movements = StockMovement.query.order_by(StockMovement.created_at.desc()).all()
    return render_template('stock_movements.html', movements=movements)

@app.route('/stock/adjustment', methods=['GET', 'POST'])
@login_required
def stock_adjustment():
    if request.method == 'POST':
        perfume_id = request.form['perfume_id']
        new_quantity = int(request.form['new_quantity'])
        reason = request.form['reason']
        notes = request.form.get('notes', '')

        perfume = Perfume.query.get_or_404(perfume_id)
        old_quantity = perfume.quantity
        difference = new_quantity - old_quantity

        # تحديث الكمية
        perfume.quantity = new_quantity

        # تسجيل حركة المخزون
        movement = StockMovement(
            perfume_id=perfume_id,
            movement_type='adjustment',
            quantity=difference,
            reason=reason,
            reference_type='manual_adjustment',
            notes=notes,
            created_by=current_user.id
        )

        db.session.add(movement)
        db.session.commit()

        flash(f'تم تعديل مخزون {perfume.name} من {old_quantity} إلى {new_quantity}', 'success')
        return redirect(url_for('stock_management'))

    perfumes = Perfume.query.filter(Perfume.is_active == True).all()
    return render_template('stock_adjustment.html', perfumes=perfumes)

# ===== التقارير =====
@app.route('/reports')
@login_required
def reports():
    return render_template('reports.html')

@app.route('/reports/sales')
@login_required
def sales_report():
    from datetime import datetime, timedelta

    # الحصول على التواريخ من الطلب
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')

    # تحويل التواريخ
    start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
    end_datetime = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)

    # الفواتير في الفترة المحددة
    invoices = Invoice.query.filter(
        Invoice.date >= start_datetime,
        Invoice.date < end_datetime
    ).order_by(Invoice.date.desc()).all()

    # إحصائيات
    total_sales = sum(inv.total for inv in invoices)
    total_invoices = len(invoices)
    avg_invoice = total_sales / total_invoices if total_invoices > 0 else 0

    # أكثر المنتجات مبيعاً
    from sqlalchemy import func
    top_products = db.session.query(
        Perfume.name,
        Perfume.company,
        func.sum(InvoiceItem.quantity).label('total_quantity'),
        func.sum(InvoiceItem.quantity * InvoiceItem.price).label('total_sales')
    ).join(InvoiceItem).join(Invoice).filter(
        Invoice.date >= start_datetime,
        Invoice.date < end_datetime
    ).group_by(Perfume.id).order_by(func.sum(InvoiceItem.quantity).desc()).limit(10).all()

    return render_template('sales_report.html',
                         invoices=invoices,
                         start_date=start_date,
                         end_date=end_date,
                         total_sales=total_sales,
                         total_invoices=total_invoices,
                         avg_invoice=avg_invoice,
                         top_products=top_products)

# ===== نظام نقاط الولاء =====
@app.route('/customers/<int:id>/loyalty')
@login_required
def customer_loyalty(id):
    customer = Customer.query.get_or_404(id)
    # حساب النقاط من الفواتير
    total_points = 0
    for invoice in customer.invoices:
        # نقطة واحدة لكل 10 جنيه
        points = int(invoice.total / 10)
        total_points += points

    # تحديث نقاط العميل
    customer.loyalty_points = total_points
    db.session.commit()

    return render_template('customer_loyalty.html', customer=customer)

@app.route('/customers/<int:id>/add-points', methods=['POST'])
@login_required
def add_loyalty_points(id):
    customer = Customer.query.get_or_404(id)
    points = int(request.form['points'])
    reason = request.form.get('reason', 'إضافة يدوية')

    customer.loyalty_points += points
    db.session.commit()

    flash(f'تمت إضافة {points} نقطة للعميل {customer.name}', 'success')
    return redirect(url_for('customer_loyalty', id=id))

@app.route('/customers/<int:id>/redeem-points', methods=['POST'])
@login_required
def redeem_loyalty_points(id):
    customer = Customer.query.get_or_404(id)
    points = int(request.form['points'])

    if points > customer.loyalty_points:
        flash('النقاط المطلوبة أكبر من النقاط المتاحة', 'danger')
    else:
        customer.loyalty_points -= points
        # يمكن إضافة خصم أو هدية هنا
        db.session.commit()
        flash(f'تم استخدام {points} نقطة للعميل {customer.name}', 'success')

    return redirect(url_for('customer_loyalty', id=id))

# ===== إدارة المستخدمين =====
@app.route('/users')
@login_required
@admin_required
def users():
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        email = request.form.get('email')
        full_name = request.form.get('full_name')
        role_id = int(request.form['role_id'])

        # فحص إذا كان اسم المستخدم موجود
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return render_template('user_form.html', title='إضافة مستخدم جديد', user=None, roles=Role.query.all())

        user = User(
            username=username,
            email=email,
            full_name=full_name,
            role_id=role_id
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        flash('تمت إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    roles = Role.query.all()
    return render_template('user_form.html', title='إضافة مستخدم جديد', user=None, roles=roles)

@app.route('/users/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    user = User.query.get_or_404(id)

    if request.method == 'POST':
        user.username = request.form['username']
        user.email = request.form.get('email')
        user.full_name = request.form.get('full_name')
        user.role_id = int(request.form['role_id'])
        user.is_active = 'is_active' in request.form

        # تغيير كلمة المرور إذا تم إدخال واحدة جديدة
        new_password = request.form.get('new_password')
        if new_password:
            user.set_password(new_password)

        db.session.commit()
        flash('تم تعديل بيانات المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    roles = Role.query.all()
    return render_template('user_form.html', title='تعديل مستخدم', user=user, roles=roles)

@app.route('/users/delete/<int:id>')
@login_required
@admin_required
def delete_user(id):
    user = User.query.get_or_404(id)

    # منع حذف المستخدم الحالي
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'danger')
    else:
        db.session.delete(user)
        db.session.commit()
        flash('تم حذف المستخدم بنجاح', 'success')

    return redirect(url_for('users'))

@app.before_request
def create_tables():
    if not hasattr(app, 'db_initialized'):
        db.create_all()

        # إنشاء الأدوار الافتراضية
        if not Role.query.first():
            import json
            roles = [
                Role(
                    name='admin',
                    description='مدير النظام - صلاحيات كاملة',
                    permissions=json.dumps([
                        'manage_users', 'manage_products', 'manage_invoices',
                        'manage_customers', 'manage_suppliers', 'view_reports',
                        'manage_stock', 'system_settings'
                    ])
                ),
                Role(
                    name='manager',
                    description='مدير فرع - صلاحيات محدودة',
                    permissions=json.dumps([
                        'manage_products', 'manage_invoices', 'manage_customers',
                        'view_reports', 'manage_stock'
                    ])
                ),
                Role(
                    name='employee',
                    description='موظف - صلاحيات أساسية',
                    permissions=json.dumps([
                        'manage_invoices', 'view_products', 'view_customers'
                    ])
                )
            ]
            for role in roles:
                db.session.add(role)

        # إنشاء المستخدم الافتراضي
        if not User.query.filter_by(username='ammar').first():
            admin_role = Role.query.filter_by(name='admin').first()
            user = User(
                username='ammar',
                full_name='عمار - مدير النظام',
                email='<EMAIL>',
                role_id=admin_role.id if admin_role else 1
            )
            user.set_password('ammar')
            db.session.add(user)

        # إنشاء فئات افتراضية
        if not Category.query.first():
            categories = [
                Category(name='عطور رجالية', description='عطور مخصصة للرجال'),
                Category(name='عطور نسائية', description='عطور مخصصة للنساء'),
                Category(name='عطور مشتركة', description='عطور يمكن استخدامها للجنسين'),
                Category(name='عطور أطفال', description='عطور مخصصة للأطفال')
            ]
            for category in categories:
                db.session.add(category)

        db.session.commit()
        app.db_initialized = True

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام إدارة العطور...")
    print("📊 النظام يعمل على: http://127.0.0.1:5000")
    print("👤 المستخدم الافتراضي: ammar / ammar")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5000)