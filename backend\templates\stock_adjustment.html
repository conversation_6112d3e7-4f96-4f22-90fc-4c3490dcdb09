<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل المخزون - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>

<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0">
            <i class="bi bi-gear"></i> تعديل مخزون المنتج
          </h5>
        </div>
        <div class="card-body">
          <!-- Flash Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }} alert-dismissible fade show">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
              {% endfor %}
            {% endif %}
          {% endwith %}

          <form method="post" novalidate>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="perfume_id" class="form-label">
                  <i class="bi bi-box"></i> اختر المنتج
                </label>
                <select name="perfume_id" id="perfume_id" class="form-select" required>
                  <option value="">-- اختر المنتج --</option>
                  {% for perfume in perfumes %}
                  <option value="{{ perfume.id }}" data-current="{{ perfume.quantity }}">
                    {{ perfume.name }} - {{ perfume.company }} (الحالي: {{ perfume.quantity }})
                  </option>
                  {% endfor %}
                </select>
                <div class="invalid-feedback">
                  يرجى اختيار منتج
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label for="new_quantity" class="form-label">
                  <i class="bi bi-hash"></i> الكمية الجديدة
                </label>
                <input type="number" name="new_quantity" id="new_quantity" class="form-control" 
                       min="0" required>
                <div class="invalid-feedback">
                  يرجى إدخال كمية صحيحة
                </div>
                <small class="form-text text-muted">
                  الكمية الحالية: <span id="current-quantity">-</span>
                </small>
              </div>
            </div>

            <div class="mb-3">
              <label for="reason" class="form-label">
                <i class="bi bi-chat-text"></i> سبب التعديل
              </label>
              <select name="reason" id="reason" class="form-select" required>
                <option value="">-- اختر السبب --</option>
                <option value="جرد">جرد دوري</option>
                <option value="تلف">تلف في المنتج</option>
                <option value="انتهاء صلاحية">انتهاء صلاحية</option>
                <option value="خطأ في الإدخال">تصحيح خطأ في الإدخال</option>
                <option value="إضافة مخزون">إضافة مخزون جديد</option>
                <option value="أخرى">أخرى</option>
              </select>
              <div class="invalid-feedback">
                يرجى اختيار سبب التعديل
              </div>
            </div>

            <div class="mb-3">
              <label for="notes" class="form-label">
                <i class="bi bi-sticky"></i> ملاحظات إضافية
              </label>
              <textarea name="notes" id="notes" class="form-control" rows="3" 
                        placeholder="أي ملاحظات إضافية حول التعديل..."></textarea>
            </div>

            <div class="alert alert-info">
              <i class="bi bi-info-circle"></i>
              <strong>تنبيه:</strong> سيتم تسجيل هذا التعديل في سجل حركات المخزون ولا يمكن التراجع عنه.
            </div>

            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-warning btn-lg">
                <i class="bi bi-gear"></i> تطبيق التعديل
              </button>
              <a href="/stock" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> العودة لإدارة المخزون
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
<script>
document.getElementById('perfume_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const currentQuantitySpan = document.getElementById('current-quantity');
    const newQuantityInput = document.getElementById('new_quantity');
    
    if (selectedOption.value) {
        const currentQuantity = selectedOption.getAttribute('data-current');
        currentQuantitySpan.textContent = currentQuantity;
        newQuantityInput.value = currentQuantity;
        newQuantityInput.focus();
    } else {
        currentQuantitySpan.textContent = '-';
        newQuantityInput.value = '';
    }
});

// تحديث الفرق عند تغيير الكمية الجديدة
document.getElementById('new_quantity').addEventListener('input', function() {
    const perfumeSelect = document.getElementById('perfume_id');
    const selectedOption = perfumeSelect.options[perfumeSelect.selectedIndex];
    
    if (selectedOption.value) {
        const currentQuantity = parseInt(selectedOption.getAttribute('data-current'));
        const newQuantity = parseInt(this.value) || 0;
        const difference = newQuantity - currentQuantity;
        
        // إظهار الفرق
        let differenceText = '';
        if (difference > 0) {
            differenceText = `زيادة: +${difference}`;
            this.style.borderColor = '#28a745';
        } else if (difference < 0) {
            differenceText = `نقص: ${difference}`;
            this.style.borderColor = '#dc3545';
        } else {
            differenceText = 'لا يوجد تغيير';
            this.style.borderColor = '#6c757d';
        }
        
        // إضافة أو تحديث عنصر عرض الفرق
        let differenceElement = document.getElementById('quantity-difference');
        if (!differenceElement) {
            differenceElement = document.createElement('small');
            differenceElement.id = 'quantity-difference';
            differenceElement.className = 'form-text';
            this.parentNode.appendChild(differenceElement);
        }
        differenceElement.textContent = differenceText;
        differenceElement.style.color = difference > 0 ? '#28a745' : difference < 0 ? '#dc3545' : '#6c757d';
    }
});
</script>
</body>
</html>
