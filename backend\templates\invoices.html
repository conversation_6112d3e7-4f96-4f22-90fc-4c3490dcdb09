<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الفواتير - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .invoice-row:hover { background-color: #f8f9fa; }
        .invoice-total { font-weight: bold; color: #198754; }
    </style>
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>
<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="bi bi-receipt"></i> سجل الفواتير</h3>
    <div>
        <a href="/invoices/add" class="btn btn-success me-2">
            <i class="bi bi-plus-circle"></i> إنشاء فاتورة جديدة
        </a>
        <a href="/invoices/export" class="btn btn-info">
            <i class="bi bi-download"></i> تصدير Excel
        </a>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-body">
      <form class="row" method="get">
        <div class="col-md-8 mb-2">
          <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" name="q" class="form-control"
                   placeholder="بحث برقم الفاتورة أو التاريخ (YYYY-MM-DD)"
                   value="{{ request.args.get('q', '') }}">
          </div>
        </div>
        <div class="col-md-2 mb-2">
          <button type="submit" class="btn btn-primary w-100">
            <i class="bi bi-search"></i> بحث
          </button>
        </div>
        <div class="col-md-2 mb-2">
          <a href="/invoices" class="btn btn-outline-secondary w-100">
            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
          </a>
        </div>
      </form>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-bordered table-hover bg-white">
      <thead class="table-light">
        <tr>
          <th>رقم الفاتورة</th>
          <th>التاريخ</th>
          <th>الإجمالي</th>
          <th>تفاصيل</th>
        </tr>
      </thead>
      <tbody>
        {% for invoice in invoices %}
        <tr class="invoice-row">
          <td>
            <strong>#{{ invoice.id }}</strong>
          </td>
          <td>
            <i class="bi bi-calendar3"></i>
            {{ invoice.date.strftime('%Y-%m-%d') }}
            <br>
            <small class="text-muted">{{ invoice.date.strftime('%H:%M') }}</small>
          </td>
          <td>
            <span class="invoice-total">{{ "{:,.2f}".format(invoice.total) }} ج.م</span>
          </td>
          <td>
            <a href="/invoices/{{ invoice.id }}" class="btn btn-sm btn-info me-1" title="عرض التفاصيل">
              <i class="bi bi-eye"></i> عرض
            </a>
            <a href="/invoices/{{ invoice.id }}/pdf" class="btn btn-sm btn-danger" title="تحميل PDF">
              <i class="bi bi-file-pdf"></i> PDF
            </a>
          </td>
        </tr>
        {% else %}
        <tr>
          <td colspan="4" class="text-center text-muted py-4">
            <i class="bi bi-receipt display-6 d-block mb-2"></i>
            لا توجد فواتير مسجلة
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>