<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>

<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="bi bi-truck"></i> إدارة الموردين</h3>
    <div>
        <a href="/suppliers/add" class="btn btn-success me-2">
            <i class="bi bi-plus-circle"></i> إضافة مورد جديد
        </a>
    </div>
  </div>
  
  <!-- Flash Messages -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }} alert-dismissible fade show">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}
  
  <div class="card shadow mb-4">
    <div class="card-body">
      <form class="row" method="get">
        <div class="col-md-8 mb-2">
          <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" name="q" class="form-control" 
                   placeholder="بحث بالاسم أو الشخص المسؤول أو رقم الهاتف" 
                   value="{{ request.args.get('q', '') }}">
          </div>
        </div>
        <div class="col-md-2 mb-2">
          <button type="submit" class="btn btn-primary w-100">
            <i class="bi bi-search"></i> بحث
          </button>
        </div>
        <div class="col-md-2 mb-2">
          <a href="/suppliers" class="btn btn-outline-secondary w-100">
            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
          </a>
        </div>
      </form>
    </div>
  </div>

  <div class="card shadow">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>اسم المورد</th>
              <th>الشخص المسؤول</th>
              <th>رقم الهاتف</th>
              <th>البريد الإلكتروني</th>
              <th>عدد المنتجات</th>
              <th>تاريخ الإضافة</th>
              <th>إجراءات</th>
            </tr>
          </thead>
          <tbody>
            {% for supplier in suppliers %}
            <tr>
              <td>
                <strong>{{ supplier.name }}</strong>
              </td>
              <td>{{ supplier.contact_person or '-' }}</td>
              <td>
                {% if supplier.phone %}
                  <a href="tel:{{ supplier.phone }}" class="text-decoration-none">
                    <i class="bi bi-telephone"></i> {{ supplier.phone }}
                  </a>
                {% else %}
                  -
                {% endif %}
              </td>
              <td>
                {% if supplier.email %}
                  <a href="mailto:{{ supplier.email }}" class="text-decoration-none">
                    <i class="bi bi-envelope"></i> {{ supplier.email }}
                  </a>
                {% else %}
                  -
                {% endif %}
              </td>
              <td>
                <span class="badge bg-info">{{ supplier.perfumes|length }}</span>
              </td>
              <td>
                <small class="text-muted">{{ supplier.created_at.strftime('%Y-%m-%d') }}</small>
              </td>
              <td class="action-buttons">
                <a href="/suppliers/edit/{{ supplier.id }}" class="btn btn-sm btn-warning" title="تعديل">
                  <i class="bi bi-pencil"></i>
                </a>
                <a href="/suppliers/delete/{{ supplier.id }}" class="btn btn-sm btn-danger" 
                   onclick="return confirm('هل أنت متأكد من حذف {{ supplier.name }}؟');" title="حذف">
                  <i class="bi bi-trash"></i>
                </a>
              </td>
            </tr>
            {% else %}
            <tr>
              <td colspan="7" class="text-center text-muted py-4">
                <i class="bi bi-truck display-6 d-block mb-2"></i>
                لا توجد موردين مسجلين
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
