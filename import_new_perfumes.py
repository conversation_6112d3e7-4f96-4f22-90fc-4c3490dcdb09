#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد العطور الجديدة من الصفحات 34-38
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_new_perfumes():
    """استيراد قائمة العطور الجديدة من الصفحات 34-38"""
    
    # بيانات العطور الجديدة
    perfumes_data = [
        # صفحة 34
        {"code": "10011", "name": "روبی نوار", "company": "Eip", "price": 1500.00},
        {"code": "891", "name": "سبارك لينج بوكيه", "company": "Eip", "price": 2950.00},
        {"code": "1144", "name": "کریزی لاف", "company": "TCf", "price": 2600.00},
        {"code": "5505", "name": "روشاذ", "company": "TCf", "price": 2490.00},
        {"code": "1045", "name": "شامبيون", "company": "TCf", "price": 3125.00},
        {"code": "1616", "name": "اسكيب حريمي", "company": "TCf", "price": 2340.00},
        {"code": "1748", "name": "استرونجر ويز يو", "company": "TCf", "price": 3525.00},
        
        # صفحة 35
        {"code": "1201", "name": "مون بلو بلو ليجيند", "company": "TCf", "price": 2990.00},
        {"code": "5628", "name": "لافی ایبل", "company": "TCf", "price": 3740.00},
        {"code": "5590", "name": "سی ارمانی", "company": "TCf", "price": 3350.00},
        {"code": "1398", "name": "اسكلبشر", "company": "TCf", "price": 2990.00},
        {"code": "5406", "name": "بلاك اوبيوم", "company": "TCf", "price": 3215.00},
        {"code": "1571", "name": "بينك شوجر", "company": "TCf", "price": 2600.00},
        {"code": "1807", "name": "هاج سنت", "company": "TCf", "price": 3375.00},
        {"code": "5714", "name": "فرى سكسى ناو", "company": "TCf", "price": 3090.00},
        {"code": "6002", "name": "تراب الذهب", "company": "TCf", "price": 3600.00},
        {"code": "3065", "name": "اسكادا شيران ذا اير", "company": "TCf", "price": 3065.00},
        {"code": "2217", "name": "انجل", "company": "TCf", "price": 2900.00},
        {"code": "5497", "name": "جاجوار جرين", "company": "TCf", "price": 2900.00},
        {"code": "5688", "name": "كلمات", "company": "TCf", "price": 3225.00},
        {"code": "6140", "name": "مضاوي", "company": "TCf", "price": 3975.00},
        {"code": "1019", "name": "باد بوی", "company": "TCf", "price": 3290.00},
        {"code": "1453", "name": "روسيندو ماتيو 5", "company": "TCf", "price": 7300.00},
        {"code": "5696", "name": "ماجی نوار", "company": "TCf", "price": 2765.00},
        {"code": "1247", "name": "وان مليون برايف", "company": "TCF", "price": 2925.00},
        {"code": "80116", "name": "ایروس فلام", "company": "غير محدد", "price": 3140.00},
        {"code": "8691", "name": "سبلاش سنت", "company": "غير محدد", "price": 4275.00},
        {"code": "1838", "name": "ماجيك الجزيرة", "company": "بارف اسانس", "price": 3775.00},
        {"code": "8380", "name": "في اي بي بلاك", "company": "بارف اسانس", "price": 3965.00},
        {"code": "6479", "name": "استرونجر ویز می", "company": "بارف اسانس", "price": 3600.00},
        {"code": "2606", "name": "جاجوار بلاك", "company": "بارف اسانس", "price": 4100.00},
        {"code": "6842", "name": "عود بوكية", "company": "بارف اسانس", "price": 8250.00},
        {"code": "7974", "name": "عروق العود", "company": "بارف اسانس", "price": 5400.00},
        {"code": "8473", "name": "مضاوی", "company": "بارف اسانس", "price": 3800.00},
        {"code": "60", "name": "سوفاج", "company": "بارف اسانس", "price": 2850.00},
        {"code": "6845", "name": "رومبا غامق", "company": "بارف اسانس", "price": 4300.00},
        {"code": "330", "name": "دانهیل ادیشن", "company": "اسانس برفيوم", "price": 2800.00},
        {"code": "38", "name": "رومبا", "company": "أسانس برفيوم", "price": 3450.00},
        {"code": "LAP001", "name": "لابيدوس", "company": "أسانس برفيوم", "price": 4750.00},
        {"code": "KHM001", "name": "خمره", "company": "اسانس برفيوم", "price": 3200.00},
        {"code": "606", "name": "عود زمزم", "company": "اسانس برفيوم", "price": 3300.00},
        {"code": "368", "name": "بربری هیر", "company": "اسانس برفيوم", "price": 3600.00},
        {"code": "7476", "name": "ازارو ونتد", "company": "اسانس برفيوم", "price": 2950.00},
        {"code": "477", "name": "هوجو نيو بوص", "company": "اسانس برفيوم", "price": 3500.00},
        {"code": "1200", "name": "اسکندال", "company": "اسانس برفيوم", "price": 2900.00},
        {"code": "5253", "name": "كوكو نت", "company": "اسانس برفيوم", "price": 4750.00},
        {"code": "118459", "name": "بيتر بيتش", "company": "باركيم", "price": 13975.00},
        {"code": "107375", "name": "سوفاج الكسير", "company": "باركيم", "price": 6490.00},
        {"code": "1655", "name": "ساید ایفکت", "company": "باركيم", "price": 3445.00},
        {"code": "50090", "name": "ایروس فیزاتشی", "company": "باركيم", "price": 4840.00},
        {"code": "1740", "name": "عود ابيض", "company": "اتركتف", "price": 2950.00},
        {"code": "60181", "name": "استرونجر ويز يو", "company": "اتركتف", "price": 5320.00},
        {"code": "60369", "name": "تونكا كولا", "company": "اتركتف", "price": 3675.00},
        {"code": "50100", "name": "اكوا فريش", "company": "اتركتف", "price": 2600.00},
        {"code": "75464", "name": "سلفر سنت", "company": "اتركتف", "price": 2900.00},
        {"code": "71905", "name": "انفكتوس فكتوري الكسير", "company": "اتركتف", "price": 2970.00},
        
        # صفحة 36
        {"code": "75448", "name": "امبريال فالی", "company": "اتركتف", "price": 3600.00},
        {"code": "70516", "name": "انی نیشان", "company": "اتركتف", "price": 5100.00},
        {"code": "71903", "name": "انفكتوس", "company": "اتركتف", "price": 2800.00},
        {"code": "60399", "name": "اتركتف عود", "company": "اتركتف", "price": 7980.00},
        {"code": "75473", "name": "ایروس فيرزاتشي", "company": "اتركتف", "price": 2920.00},
        {"code": "66284", "name": "اربابورا", "company": "اتركتف", "price": 3900.00},
        {"code": "72115", "name": "اولمبيا", "company": "اتركتف", "price": 3500.00},
        {"code": "71356", "name": "بلاك اوركيد", "company": "اتركتف", "price": 3180.00},
        {"code": "50099", "name": "بينك شوجر", "company": "اتركتف", "price": 2320.00},
        {"code": "73820", "name": "بكرات روج", "company": "اتركتف", "price": 4800.00},
        {"code": "72987", "name": "بی ام دبلیو", "company": "اتركتف", "price": 3330.00},
        {"code": "75165", "name": "جیمی شو", "company": "اتركتف", "price": 3750.00},
        {"code": "50105", "name": "ديلينا لاروز", "company": "اتركتف", "price": 4480.00},
        {"code": "72310", "name": "عود بوكيه مركز", "company": "اتركتف", "price": 2850.00},
        {"code": "71900", "name": "ليبر (سان لوران)", "company": "اتركتف", "price": 2940.00},
        {"code": "71901", "name": "لاف ذا هفنلی", "company": "اتركتف", "price": 2880.00},
        {"code": "73652", "name": "لافی ایبل اکستریت", "company": "اتركتف", "price": 3250.00},
        {"code": "71360", "name": "میس دیور بلومينج بوكيه", "company": "اتركتف", "price": 2980.00},
        {"code": "50093", "name": "وان مان شو", "company": "اتركتف", "price": 2940.00},
        {"code": "50386", "name": "شيروتی", "company": "اتركتف", "price": 2740.00},
        {"code": "51336", "name": "بلاك ليكسز", "company": "اتركتف", "price": 3980.00},
        {"code": "55288", "name": "مای وای برفیوم", "company": "اتركتف", "price": 2670.00},
        {"code": "50097", "name": "هوجو", "company": "اتركتف", "price": 2840.00},
        {"code": "55320", "name": "توباكو فانيليا", "company": "اتركتف", "price": 3250.00},
        {"code": "50106", "name": "فانتوم", "company": "اتركتف", "price": 3160.00},
        {"code": "50119", "name": "بكرات روج", "company": "اتركتف", "price": 3720.00},
        {"code": "75447", "name": "امبريال فالی مرکز", "company": "اتركتف", "price": 7700.00},
        {"code": "74005", "name": "خالص مسك", "company": "اتركتف", "price": 3040.00},
        {"code": "LT001", "name": "استرونجر ويز يو", "company": "لوتس", "price": 3845.00},
        {"code": "LT002", "name": "ايروس فيرزاتشي", "company": "لوتس", "price": 3950.00},
        {"code": "LT003", "name": "عود الامير", "company": "لوتس", "price": 3030.00},
        {"code": "LT004", "name": "اوليمبيا", "company": "لوتس", "price": 3500.00},
        {"code": "LT005", "name": "باد بوی", "company": "لوتس", "price": 4035.00},
        {"code": "LT006", "name": "بلاك اوبيوم", "company": "لوتس", "price": 3350.00},
        {"code": "LT007", "name": "جیمی شو حریمی ورجالی", "company": "لوتس", "price": 3845.00},
        {"code": "LT008", "name": "جیمی شو برمیوم", "company": "لوتس", "price": 5555.00},
        {"code": "LT009", "name": "زارا فور هيم", "company": "لوتس", "price": 4035.00},
        {"code": "LT010", "name": "سوفاج", "company": "لوتس", "price": 3285.00},
        {"code": "LT011", "name": "سوفاج اود برفيوم", "company": "لوتس", "price": 5120.00},
        {"code": "LT012", "name": "سي ارمانی", "company": "لوتس", "price": 3165.00},
        {"code": "LT013", "name": "لو بو", "company": "لوتس", "price": 5555.00},
        {"code": "LT014", "name": "ليالي الشوق", "company": "لوتس", "price": 3845.00},
        {"code": "LT015", "name": "مرسيدس (مان بلو)", "company": "لوتس", "price": 3300.00},
        {"code": "LT016", "name": "مون بلو ليجند", "company": "لوتس", "price": 3845.00},
        {"code": "LT017", "name": "وان میلیون", "company": "لوتس", "price": 3330.00},
        {"code": "LT018", "name": "هاج سنت", "company": "لوتس", "price": 3950.00},
        {"code": "LT019", "name": "دريفين دانهيل", "company": "لوتس", "price": 3970.00},
        {"code": "LT020", "name": "جود جيرل دراما", "company": "لوتس", "price": 3230.00},
        {"code": "LT021", "name": "فلورال امبر سنشوال", "company": "لوتس", "price": 16500.00},

        # صفحة 37
        {"code": "LT022", "name": "ایما جنیشن", "company": "لوتس", "price": 29500.00},
        {"code": "LT023", "name": "بورن توبی انفور جیتابل", "company": "لوتس", "price": 18500.00},
        {"code": "LT024", "name": "جريس تشارنيل", "company": "لوتس", "price": 14950.00},
        {"code": "LT025", "name": "مضاوي", "company": "لوتس", "price": 6600.00},
        {"code": "LT026", "name": "جوتشي بلوم", "company": "لوتس", "price": 3880.00},
        {"code": "LZ001", "name": "اربا بورا", "company": "لوزی", "price": 9760.00},
        {"code": "LZ002", "name": "انفكتوس", "company": "لوزی", "price": 4950.00},
        {"code": "LZ003", "name": "برایت کریستال", "company": "لوزی", "price": 4845.00},
        {"code": "LZ004", "name": "ألين رجالي", "company": "لوزی", "price": 6130.00},
        {"code": "LZ005", "name": "بكرات روج", "company": "لوزی", "price": 9965.00},
        {"code": "LZ006", "name": "بلاتنيوم", "company": "لوزی", "price": 4715.00},
        {"code": "LZ007", "name": "بلاك افغانو", "company": "لوزی", "price": 16500.00},
        {"code": "LZ008", "name": "بلاك اكس اس", "company": "لوزی", "price": 4245.00},
        {"code": "LZ009", "name": "بلاك ليكزس", "company": "لوزی", "price": 4500.00},
        {"code": "LZ010", "name": "تشانس", "company": "لوزی", "price": 6300.00},
        {"code": "LZ011", "name": "تومی فریدوم", "company": "لوزی", "price": 5385.00},
        {"code": "LZ012", "name": "ثری جی", "company": "لوزی", "price": 3790.00},
        {"code": "LZ013", "name": "جود جيرل", "company": "لوزی", "price": 4305.00},
        {"code": "LZ014", "name": "سبايس بومب كستريم", "company": "لوزی", "price": 5375.00},
        {"code": "LZ015", "name": "سلطان العطور A", "company": "لوزی", "price": 13465.00},
        {"code": "LZ016", "name": "سلفر سنت", "company": "لوزی", "price": 5375.00},
        {"code": "LZ017", "name": "سلفر كورس", "company": "لوزی", "price": 6195.00},
        {"code": "LZ018", "name": "سوفاج", "company": "لوزی", "price": 5555.00},
        {"code": "LZ019", "name": "سوفاج لكسير", "company": "لوزی", "price": 4845.00},
        {"code": "LZ020", "name": "سویت کوفی", "company": "لوزی", "price": 6870.00},
        {"code": "LZ021", "name": "سي أرماني", "company": "لوزی", "price": 5875.00},
        {"code": "LZ022", "name": "سی باشون احمر", "company": "لوزی", "price": 3550.00},
        {"code": "LZ023", "name": "سيكسي 212 حريمي", "company": "لوزی", "price": 4640.00},
        {"code": "LZ024", "name": "سيكسي 212 رجالي", "company": "لوزی", "price": 5375.00},
        {"code": "LZ025", "name": "غرام", "company": "لوزی", "price": 4710.00},
        {"code": "LZ026", "name": "فلير ناركوتيك", "company": "لوزی", "price": 9985.00},
        {"code": "LZ027", "name": "فوياج", "company": "لوزی", "price": 4450.00},
        {"code": "LZ028", "name": "فی ای بی روز", "company": "لوزی", "price": 5315.00},
        {"code": "LZ029", "name": "كروم ليجند", "company": "لوزی", "price": 5375.00},
        {"code": "LZ030", "name": "كلمات", "company": "لوزی", "price": 9160.00},
        {"code": "LZ031", "name": "لاف ذا هفنلی", "company": "لوزی", "price": 4450.00},
        {"code": "LZ032", "name": "لافی ای بل", "company": "لوزی", "price": 5555.00},
        {"code": "LZ033", "name": "لاكوست وايت", "company": "لوزی", "price": 4450.00},
        {"code": "LZ034", "name": "مسك ابيض", "company": "لوزی", "price": 2635.00},
        {"code": "LZ035", "name": "مون بلو", "company": "لوزی", "price": 6535.00},
        {"code": "LZ036", "name": "ميس كوكو", "company": "لوزی", "price": 4980.00},
        {"code": "LZ037", "name": "مسك الكعبة", "company": "لوزی", "price": 4450.00},
        {"code": "LZ038", "name": "وصال", "company": "لوزی", "price": 5700.00},
        {"code": "LZ039", "name": "وای", "company": "لوزی", "price": 5375.00},
        {"code": "LZ040", "name": "امبر بیزر", "company": "لوزی", "price": 9985.00},
        {"code": "LZ041", "name": "نيكسوس", "company": "لوزی", "price": 7945.00},
        {"code": "LZ042", "name": "لوست شیری", "company": "لوزی", "price": 6060.00},
        {"code": "LZ043", "name": "ميس بيوتي", "company": "لوزی", "price": 5250.00},
        {"code": "LZ044", "name": "فودكا ان ذا روكس", "company": "لوزی", "price": 7740.00},

        # صفحة 38
        {"code": "LZ045", "name": "هابي كلينك", "company": "لوزی", "price": 7500.00},
        {"code": "LZ046", "name": "مسك الرمان", "company": "لوزی", "price": 4950.00},
        {"code": "LZ047", "name": "ذا وان رجالي", "company": "لوزی", "price": 7410.00},
        {"code": "LZ048", "name": "فودكا ان ذا روكس", "company": "لوزی", "price": 7740.00},
        {"code": "LZ049", "name": "هابي كلينك M", "company": "لوزی", "price": 7500.00},
        {"code": "LZ050", "name": "الكسندريا 3 زيرجوف", "company": "لوزی", "price": 9160.00},
        {"code": "LZ051", "name": "وایت کرید", "company": "لوزی", "price": 5520.00},
        {"code": "LZ052", "name": "ايدول", "company": "لوزی", "price": 5200.00},
        {"code": "LZ053", "name": "مسك فانيليا", "company": "لوزی", "price": 4950.00},
        {"code": "LZ054", "name": "بار اوندا (ناسو ماتو)", "company": "لوزی", "price": 17950.00},
        {"code": "LZ055", "name": "مسك الجسم", "company": "لوزی", "price": 7740.00},
        {"code": "LZ056", "name": "كريد سلفر", "company": "لوزی", "price": 5520.00},
        {"code": "LZ057", "name": "کیرکی", "company": "لوزی", "price": 7945.00},
        {"code": "LZ058", "name": "ديفيد بيكهام اسانسيه", "company": "لوزی", "price": 5120.00},
        {"code": "LZ059", "name": "مسك بودر", "company": "لوزی", "price": 4950.00},
        {"code": "LZ060", "name": "بلاك اوبيوم اوفر ريد", "company": "لوزی", "price": 3750.00},
        {"code": "LZ061", "name": "اوجان برفيوم دي مارلی", "company": "لوزی", "price": 3500.00},
        {"code": "LZ062", "name": "لافی ایبل انتنس", "company": "لوزی", "price": 3950.00},
        {"code": "LZ063", "name": "لامال لا برفيوم", "company": "لوزی", "price": 2950.00},
        {"code": "LZ064", "name": "الثائر دي مارلي", "company": "لوزی", "price": 6500.00},
        {"code": "LZ065", "name": "بربي جولد", "company": "لوزی", "price": 3300.00},
        {"code": "LZ066", "name": "اليرو سبورت", "company": "لوزی", "price": 5790.00},
        {"code": "LZ067", "name": "ترى هيرمس", "company": "لوزی", "price": 6195.00},
        {"code": "LZ068", "name": "فانتوم", "company": "لوزی", "price": 4710.00},
        {"code": "LZ069", "name": "جورجينا سنس لافرن", "company": "لوزی", "price": 2950.00},
        {"code": "LZ070", "name": "كريستال نوار فرزاتشی", "company": "لوزی", "price": 5315.00},
    ]
    
    with app.app_context():
        try:
            # إنشاء أو الحصول على الموردين الجدد
            companies = set([item["company"] for item in perfumes_data])
            suppliers = {}
            
            for company_name in companies:
                supplier = Supplier.query.filter_by(name=company_name).first()
                if not supplier:
                    supplier = Supplier(
                        name=company_name,
                        contact_person="غير محدد",
                        phone="غير محدد",
                        address="غير محدد"
                    )
                    db.session.add(supplier)
                    db.session.flush()  # للحصول على ID
                suppliers[company_name] = supplier
            
            # إضافة العطور
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = suppliers[perfume_data["company"]]
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - كود: {perfume_data['code']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=0,  # كمية افتراضية
                        min_quantity=5,  # حد أدنى افتراضي
                        sku=perfume_data["code"],
                        supplier=suppliers[perfume_data["company"]],
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - كود: {perfume_data['code']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")
            print(f"📊 إجمالي العطور المعالجة: {len(perfumes_data)}")
            print(f"🏢 عدد الموردين الجدد: {len(companies)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد العطور الجديدة من الصفحات 34-36...")
    print("=" * 60)
    
    success = import_new_perfumes()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("يمكنك الآن تشغيل النظام ومراجعة العطور الجديدة.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
