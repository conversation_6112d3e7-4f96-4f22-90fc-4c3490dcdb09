#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات وإضافة البيانات الافتراضية
"""

import os
import sys
import json
from datetime import datetime, date

# إضافة مجلد backend للمسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app import app
from backend.models import db, User, Role, Category, Supplier, Customer, Perfume

def init_database():
    """إنشاء قاعدة البيانات والبيانات الافتراضية"""
    
    with app.app_context():
        print("🗄️  إنشاء قاعدة البيانات...")
        
        # حذف الجداول الموجودة وإنشاء جداول جديدة
        db.drop_all()
        db.create_all()
        
        print("✅ تم إنشاء الجداول بنجاح")
        
        # إنشاء الأدوار
        print("👥 إنشاء الأدوار...")
        roles = [
            Role(
                name='admin',
                description='مدير النظام - صلاحيات كاملة',
                permissions=json.dumps([
                    'manage_users', 'manage_products', 'manage_invoices',
                    'manage_customers', 'manage_suppliers', 'view_reports',
                    'manage_stock', 'system_settings'
                ])
            ),
            Role(
                name='manager',
                description='مدير فرع - صلاحيات محدودة',
                permissions=json.dumps([
                    'manage_products', 'manage_invoices', 'manage_customers',
                    'view_reports', 'manage_stock'
                ])
            ),
            Role(
                name='employee',
                description='موظف - صلاحيات أساسية',
                permissions=json.dumps([
                    'manage_invoices', 'view_products', 'view_customers'
                ])
            )
        ]
        
        for role in roles:
            db.session.add(role)
        
        db.session.commit()
        print("✅ تم إنشاء الأدوار بنجاح")
        
        # إنشاء المستخدمين
        print("👤 إنشاء المستخدمين...")
        admin_role = Role.query.filter_by(name='admin').first()
        manager_role = Role.query.filter_by(name='manager').first()
        employee_role = Role.query.filter_by(name='employee').first()
        
        users = [
            {
                'username': 'ammar',
                'password': 'ammar',
                'full_name': 'عمار - مدير النظام',
                'email': '<EMAIL>',
                'role': admin_role
            },
            {
                'username': 'manager',
                'password': 'manager123',
                'full_name': 'مدير الفرع',
                'email': '<EMAIL>',
                'role': manager_role
            },
            {
                'username': 'employee',
                'password': 'employee123',
                'full_name': 'موظف المبيعات',
                'email': '<EMAIL>',
                'role': employee_role
            }
        ]
        
        for user_data in users:
            user = User(
                username=user_data['username'],
                full_name=user_data['full_name'],
                email=user_data['email'],
                role_id=user_data['role'].id
            )
            user.set_password(user_data['password'])
            db.session.add(user)
        
        db.session.commit()
        print("✅ تم إنشاء المستخدمين بنجاح")
        
        # إنشاء الفئات
        print("📂 إنشاء فئات العطور...")
        categories = [
            Category(name='عطور رجالية', description='عطور مخصصة للرجال'),
            Category(name='عطور نسائية', description='عطور مخصصة للنساء'),
            Category(name='عطور مشتركة', description='عطور يمكن استخدامها للجنسين'),
            Category(name='عطور أطفال', description='عطور مخصصة للأطفال'),
            Category(name='عطور شرقية', description='عطور بروائح شرقية تقليدية'),
            Category(name='عطور فرنسية', description='عطور فرنسية فاخرة')
        ]
        
        for category in categories:
            db.session.add(category)
        
        db.session.commit()
        print("✅ تم إنشاء الفئات بنجاح")
        
        # إنشاء موردين تجريبيين
        print("🚚 إنشاء الموردين...")
        suppliers = [
            Supplier(
                name='شركة العطور الذهبية',
                contact_person='أحمد محمد',
                phone='01234567890',
                email='<EMAIL>',
                address='القاهرة، مصر'
            ),
            Supplier(
                name='مؤسسة الروائح الفاخرة',
                contact_person='فاطمة علي',
                phone='01987654321',
                email='<EMAIL>',
                address='الإسكندرية، مصر'
            ),
            Supplier(
                name='بيت العطور الشرقية',
                contact_person='محمود حسن',
                phone='01122334455',
                email='<EMAIL>',
                address='الجيزة، مصر'
            )
        ]
        
        for supplier in suppliers:
            db.session.add(supplier)
        
        db.session.commit()
        print("✅ تم إنشاء الموردين بنجاح")
        
        # إنشاء عملاء تجريبيين
        print("👥 إنشاء العملاء...")
        customers = [
            Customer(
                name='سارة أحمد',
                phone='01111111111',
                email='<EMAIL>',
                gender='female',
                loyalty_points=150
            ),
            Customer(
                name='محمد علي',
                phone='01222222222',
                email='<EMAIL>',
                gender='male',
                loyalty_points=200
            ),
            Customer(
                name='نور الدين',
                phone='01333333333',
                email='<EMAIL>',
                gender='male',
                loyalty_points=75
            )
        ]
        
        for customer in customers:
            db.session.add(customer)
        
        db.session.commit()
        print("✅ تم إنشاء العملاء بنجاح")
        
        # إنشاء عطور تجريبية
        print("🌸 إنشاء العطور التجريبية...")
        
        # الحصول على المراجع
        men_category = Category.query.filter_by(name='عطور رجالية').first()
        women_category = Category.query.filter_by(name='عطور نسائية').first()
        unisex_category = Category.query.filter_by(name='عطور مشتركة').first()
        supplier1 = Supplier.query.first()
        supplier2 = Supplier.query.offset(1).first()
        
        perfumes = [
            Perfume(
                name='عود ملكي',
                company='العطور الذهبية',
                price=250.00,
                cost_price=180.00,
                quantity=50,
                min_quantity=10,
                size='100ml',
                category_id=men_category.id,
                supplier_id=supplier1.id,
                expiry_date=date(2026, 12, 31),
                location='رف A1'
            ),
            Perfume(
                name='ورد دمشقي',
                company='الروائح الفاخرة',
                price=180.00,
                cost_price=120.00,
                quantity=30,
                min_quantity=5,
                size='75ml',
                category_id=women_category.id,
                supplier_id=supplier2.id,
                expiry_date=date(2026, 6, 30),
                location='رف B2'
            ),
            Perfume(
                name='مسك الليل',
                company='العطور الذهبية',
                price=320.00,
                cost_price=240.00,
                quantity=25,
                min_quantity=8,
                size='50ml',
                category_id=unisex_category.id,
                supplier_id=supplier1.id,
                expiry_date=date(2027, 3, 15),
                location='رف C1'
            ),
            Perfume(
                name='عنبر أصيل',
                company='بيت العطور',
                price=450.00,
                cost_price=350.00,
                quantity=15,
                min_quantity=5,
                size='100ml',
                category_id=men_category.id,
                supplier_id=supplier1.id,
                expiry_date=date(2026, 9, 20),
                location='رف A3'
            ),
            Perfume(
                name='ياسمين شامي',
                company='الروائح الفاخرة',
                price=200.00,
                cost_price=140.00,
                quantity=40,
                min_quantity=10,
                size='75ml',
                category_id=women_category.id,
                supplier_id=supplier2.id,
                expiry_date=date(2026, 11, 10),
                location='رف B1'
            )
        ]
        
        for perfume in perfumes:
            db.session.add(perfume)
        
        db.session.commit()
        print("✅ تم إنشاء العطور التجريبية بنجاح")
        
        print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print("👤 مدير النظام: ammar / ammar")
        print("👤 مدير الفرع: manager / manager123")
        print("👤 موظف المبيعات: employee / employee123")
        print("\n" + "="*50)

if __name__ == '__main__':
    init_database()
