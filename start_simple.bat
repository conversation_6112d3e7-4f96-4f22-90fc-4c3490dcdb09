@echo off
title Perfume Store System

echo Starting Perfume Store Management System...
echo.

REM Check if virtual environment exists
if not exist "venv\" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install dependencies if needed
pip show flask >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
)

REM Create database if needed
if not exist "data\store.db" (
    echo Creating database...
    python create_db.py
)

echo.
echo System starting on: http://127.0.0.1:5000
echo Default login: ammar / ammar
echo Press Ctrl+C to stop
echo.

python run.py

pause
