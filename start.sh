#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo ""
echo "========================================"
echo "       🌸 نظام إدارة العطور 🌸"
echo "========================================"
echo ""

echo "📋 فحص المتطلبات..."

# فحص Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 غير مثبت. يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

echo "✅ Python مثبت"

# فحص البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ فشل في إنشاء البيئة الافتراضية"
        exit 1
    fi
fi

echo "🔄 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# فحص التبعيات
echo "📦 فحص التبعيات..."
if ! pip show flask &> /dev/null; then
    echo "📥 تثبيت التبعيات..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت التبعيات"
        exit 1
    fi
fi

echo "✅ التبعيات مثبتة"

# فحص قاعدة البيانات
if [ ! -f "data/store.db" ]; then
    echo "🗄️ إنشاء قاعدة البيانات..."
    python create_db.py
    if [ $? -ne 0 ]; then
        echo "❌ فشل في إنشاء قاعدة البيانات"
        exit 1
    fi
fi

echo "✅ قاعدة البيانات جاهزة"

echo ""
echo "🚀 بدء تشغيل النظام..."
echo "📊 النظام سيعمل على: http://127.0.0.1:5000"
echo "👤 المستخدم الافتراضي: ammar / ammar"
echo ""
echo "⚠️  لإيقاف النظام اضغط Ctrl+C"
echo "========================================"
echo ""

# تشغيل النظام
python run.py

echo ""
echo "👋 تم إيقاف النظام"
