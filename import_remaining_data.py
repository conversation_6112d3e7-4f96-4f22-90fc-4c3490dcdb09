#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد باقي البيانات من الموردين الجدد
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_remaining_data():
    """استيراد باقي البيانات"""
    
    # بيانات العطور المتبقية
    perfumes_data = [
        # ese - مجموعة كبيرة
        {"code": "975166", "name": "اسكادا تاج", "company": "ese", "price": 2673.00},
        {"code": "4019", "name": "اسكادا كولكشن", "company": "ese", "price": 1935.00},
        {"code": "R503", "name": "اسكادا مجناتيزم", "company": "ese", "price": 2961.00},
        {"code": "51111", "name": "اسكادا مون سباركل", "company": "ese", "price": 2331.00},
        {"code": "15110", "name": "اسكادا مون سباركل", "company": "ese", "price": 2673.00},
        {"code": "107E", "name": "اسكادا مون سباركل (نخب اول)", "company": "ese", "price": 3051.00},
        {"code": "121404", "name": "اسكيب", "company": "ese", "price": 2151.00},
        {"code": "121405", "name": "اسكيب", "company": "ese", "price": 2223.00},
        {"code": "97511", "name": "اسكيب (نخب اول)", "company": "ese", "price": 2601.00},
        {"code": "70121", "name": "اغراء", "company": "ese", "price": 3670.00},
        {"code": "97524", "name": "اماریج", "company": "ese", "price": 2128.00},
        {"code": "123E", "name": "اورجانزا", "company": "ese", "price": 2196.00},
        {"code": "9901", "name": "اورجانزا", "company": "ese", "price": 2452.00},
        {"code": "97559", "name": "اولمبيا", "company": "ese", "price": 3231.00},
        {"code": "127", "name": "اولمبيا (نخب اول)", "company": "ese", "price": 3852.00},
        {"code": "70112", "name": "أميرة العرب (نخب اول)", "company": "ese", "price": 2673.00},
        {"code": "70110", "name": "باريس هيلتون", "company": "ese", "price": 2682.00},
        {"code": "97581", "name": "بامب شيل (نخب اول)", "company": "ese", "price": 3748.00},
        {"code": "11130", "name": "بربری هیر", "company": "ese", "price": 2690.00},
        {"code": "70114", "name": "بربری هیر (نخب اول)", "company": "ese", "price": 2970.00},
        {"code": "51101", "name": "بريتني", "company": "ese", "price": 2322.00},
        {"code": "51102", "name": "بريتني", "company": "ese", "price": 2452.00},
        {"code": "121E", "name": "بلاك اوبيوم", "company": "ese", "price": 2574.00},
        {"code": "97571", "name": "بلاك اوبيوم", "company": "ese", "price": 3492.00},
        {"code": "56003", "name": "بلاك اوبيوم", "company": "ese", "price": 3748.00},
        {"code": "97544", "name": "بلاك اوركيد", "company": "ese", "price": 2583.00},
        {"code": "97555", "name": "بلاك ايس", "company": "ese", "price": 2367.00},
        {"code": "10109", "name": "بونبون", "company": "ese", "price": 2970.00},
        {"code": "116", "name": "بنادورا", "company": "ese", "price": 2452.00},
        {"code": "1501", "name": "بنك شوجر", "company": "ese", "price": 2241.00},
        {"code": "97505", "name": "بنك شوجر (نخب اول)", "company": "ese", "price": 2673.00},
        {"code": "70148", "name": "بیور سیدکش نخب اول)", "company": "ese", "price": 3590.00},
        {"code": "97519", "name": "توسكا", "company": "ese", "price": 2574.00},
        {"code": "97595", "name": "جادور (نخب اول)", "company": "ese", "price": 3208.00},
        {"code": "70119", "name": "1000 جادور", "company": "ese", "price": 3573.00},
        {"code": "102233", "name": "جادور جولد", "company": "ese", "price": 2452.00},
        {"code": "977575", "name": "جادور جولد", "company": "ese", "price": 2961.00},
        {"code": "70188", "name": "جوتشی رش (نخب اول)", "company": "ese", "price": 3970.00},
        {"code": "97402", "name": "جود جيرل", "company": "ese", "price": 3100.00},
        {"code": "70194", "name": "جود جيرل (نخب اول)", "company": "ese", "price": 3890.00},
        {"code": "975105", "name": "جويريه", "company": "ese", "price": 3492.00},
        {"code": "70106", "name": "حريم السلطان", "company": "ese", "price": 2970.00},
        {"code": "70134", "name": "راش فيكتوريا", "company": "ese", "price": 3100.00},
        {"code": "204002", "name": "رالف", "company": "ese", "price": 2583.00},
        {"code": "120E", "name": "رالف (نخب اول)", "company": "ese", "price": 2844.00},
        {"code": "70149", "name": "روبرتو كافالی (نخب اول)", "company": "ese", "price": 3970.00},
        {"code": "1003E", "name": "رومبا", "company": "ese", "price": 2421.00},
        {"code": "HR97525", "name": "رومبا HR", "company": "ese", "price": 2844.00},
        {"code": "1002E", "name": "رومبا بلاك", "company": "ese", "price": 2128.00},
        {"code": "97546", "name": "زارا بينك", "company": "ese", "price": 2574.00},
        {"code": "13908", "name": "زارا موف", "company": "ese", "price": 2871.00},
        {"code": "97529", "name": "سكاندال 1000", "company": "ese", "price": 3100.00},
        {"code": "1136", "name": "سنشوال", "company": "ese", "price": 2970.00},
        {"code": "22210", "name": "سی ارماني", "company": "ese", "price": 2673.00},
        {"code": "97545", "name": "سی باشن", "company": "ese", "price": 3208.00},
        {"code": "97577", "name": "سی باشن (نخب اول)", "company": "ese", "price": 3852.00},
        {"code": "70155", "name": "سينس جورجينا", "company": "ese", "price": 3470.00},
        {"code": "97525E", "name": "شیلز", "company": "ese", "price": 2844.00},
        {"code": "97518", "name": "شیلز", "company": "ese", "price": 2682.00},
        {"code": "70120", "name": "شیلز (نخب اول)", "company": "ese", "price": 3447.00},
        {"code": "70190", "name": "صبايا (نخب اول)", "company": "ese", "price": 3470.00},
        {"code": "70104", "name": "فاراوی", "company": "ese", "price": 2370.00},
        {"code": "975102", "name": "فرى سكسى ناو", "company": "ese", "price": 3141.00},
        {"code": "97513", "name": "فيكتوريا انجل", "company": "ese", "price": 2574.00},
        {"code": "97540", "name": "فيكتوريا انجل میست", "company": "ese", "price": 2844.00},
        {"code": "97514", "name": "فيكتوريا ريد", "company": "ese", "price": 2673.00},
        {"code": "975101", "name": "قصة حب", "company": "ese", "price": 3208.00},
        {"code": "97565", "name": "كاريزما", "company": "ese", "price": 2844.00},
        {"code": "70147", "name": "كاريزما", "company": "ese", "price": 2570.00},
        {"code": "18633", "name": "كاسليا", "company": "ese", "price": 2196.00},
        {"code": "97536", "name": "كاسليا (نخب اول)", "company": "ese", "price": 2583.00},
        {"code": "801010", "name": "کریزی لاف", "company": "ese", "price": 2421.00},
        {"code": "97552", "name": "کریزی لاف نخب اول)", "company": "ese", "price": 2871.00},
        {"code": "115E", "name": "کشمیر", "company": "ese", "price": 2452.00},
        {"code": "70139", "name": "كوكو نت - فيكتوريا", "company": "ese", "price": 3123.00},
        {"code": "r1002", "name": "لاف اذ هفنلی", "company": "ese", "price": 3582.00},
        {"code": "97573", "name": "لافي بيل", "company": "ese", "price": 3231.00},
        {"code": "97588", "name": "لافي بيل (نخب اول)", "company": "ese", "price": 3501.00},
        {"code": "70141", "name": "ليبر انتنس", "company": "ese", "price": 3390.00},
        {"code": "70107", "name": "مودرن برنسس", "company": "ese", "price": 2970.00},
        {"code": "975106", "name": "ميار - لطافة", "company": "ese", "price": 2970.00},
        {"code": "97566", "name": "مید نایت", "company": "ese", "price": 2844.00},
        {"code": "16614", "name": "هاوای", "company": "ese", "price": 2061.00},
        {"code": "10123", "name": "هاوای (نخب اول)", "company": "ese", "price": 2490.00},
        {"code": "113E", "name": "هيفين", "company": "ese", "price": 2844.00},
        {"code": "97522", "name": "ويت شوكليت", "company": "ese", "price": 2511.00},
        {"code": "97580", "name": "ويت شوكليت بلاس", "company": "ese", "price": 2673.00},
        {"code": "70136E", "name": "ويك اند", "company": "ese", "price": 3141.00},
        {"code": "13315", "name": "ويك اند", "company": "ese", "price": 2241.00},
        {"code": "70115", "name": "ويك اند (نخب اول)", "company": "ese", "price": 2970.00},
        {"code": "70108", "name": "يارا - لطافة", "company": "ese", "price": 2970.00},
        {"code": "109E", "name": "يارا نخب اول", "company": "ese", "price": 3690.00},
        {"code": "97537", "name": "الاكسندريا (نخب اول)", "company": "ese", "price": 6102.00},
        {"code": "975108", "name": "امبر نومید", "company": "ese", "price": 3490.00},
        {"code": "904", "name": "انجل شیر", "company": "ese", "price": 5148.00},
        {"code": "70101", "name": "باراوندا", "company": "ese", "price": 7970.00},
        {"code": "R/102", "name": "بكرات روج", "company": "ese", "price": 4788.00},
        {"code": "70153", "name": "بكرات روج", "company": "ese", "price": 3370.00},
        {"code": "97530", "name": "بكرات روج (نخب اول)", "company": "ese", "price": 6561.00},
        {"code": "70131", "name": "توباكو فانيلا", "company": "ese", "price": 4221.00},
        {"code": "975104", "name": "خمرة (نخب اول)", "company": "ese", "price": 3970.00},
        {"code": "70137", "name": "خمره - لطافة", "company": "ese", "price": 3411.00},
        {"code": "4970", "name": "خمرة قهوة نخب اول)", "company": "ese", "price": 4970.00},
        {"code": "70142", "name": "دوف", "company": "ese", "price": 1880.00},
        {"code": "97523", "name": "روز فانيل", "company": "ese", "price": 2907.00},
        {"code": "97527", "name": "روز فانیل (نخب اول)", "company": "ese", "price": 4396.00},
        {"code": "10116", "name": "سحر الكلمات", "company": "ese", "price": 2370.00},
        {"code": "10119", "name": "فانيليا احادی", "company": "ese", "price": 2980.00},
        {"code": "5051", "name": "عود بوكيه", "company": "ese", "price": 2574.00},
        {"code": "97591", "name": "عود بوكيه (نخب اول)", "company": "ese", "price": 3573.00},
        {"code": "97560", "name": "عود بوكيه 1000", "company": "ese", "price": 3501.00},
        {"code": "70130E", "name": "ناكسوس زار جوف", "company": "ese", "price": 5382.00},
        {"code": "10121", "name": "اسد - لطافة", "company": "ese", "price": 5970.00},
        {"code": "6970", "name": "اسد - لطافة (نخب اول)", "company": "ese", "price": 6970.00},
        {"code": "97558", "name": "الحجر الاسود (نخب اول)", "company": "ese", "price": 5175.00},
        {"code": "70146", "name": "بديع العود", "company": "ese", "price": 3970.00},
        {"code": "70124", "name": "بلاك افغانو اسود", "company": "ese", "price": 8970.00},
        {"code": "3133", "name": "بلاك عود", "company": "ese", "price": 4396.00},
        {"code": "97556", "name": "بلاك عود (نخب اول)", "company": "ese", "price": 5166.00},
        {"code": "97538", "name": "دعاء الجنة", "company": "ese", "price": 2043.00},
        {"code": "70150", "name": "دهن العود الابيض (نخب اول)", "company": "ese", "price": 5970.00},
        {"code": "130E", "name": "سلطان", "company": "ese", "price": 9873.00},
        {"code": "1135", "name": "صندل", "company": "ese", "price": 3470.00},
        {"code": "97551", "name": "عود ابيض نخب اول)", "company": "ese", "price": 5148.00},
        {"code": "97596", "name": "عود الفريد", "company": "ese", "price": 5031.00},
        {"code": "70126", "name": "عود المقام", "company": "ese", "price": 4970.00},
        {"code": "97539", "name": "عود شهرة (نخب اول)", "company": "ese", "price": 3231.00},
        {"code": "3198E", "name": "عود شيخة", "company": "ese", "price": 2844.00},
        {"code": "21111", "name": "عود عمانی", "company": "ese", "price": 2421.00},
        {"code": "70117", "name": "عود كلمات (نخب اول)", "company": "ese", "price": 4473.00},
        {"code": "70116", "name": "عود كلمات 1000", "company": "ese", "price": 2970.00},
        {"code": "70111", "name": "عود کمبودی", "company": "ese", "price": 11610.00},
        {"code": "70100", "name": "فخامة السلطان", "company": "ese", "price": 6975.00},
        {"code": "105E", "name": "فل", "company": "ese", "price": 1773.00},
        {"code": "9759E", "name": "فل", "company": "ese", "price": 2065.00},
        {"code": "106E", "name": "فواكه", "company": "ese", "price": 2065.00},
        {"code": "7106", "name": "فواكه سعودی", "company": "ese", "price": 2421.00},
        {"code": "70109", "name": "قصة عود (نخب اول)", "company": "ese", "price": 5970.00},
        {"code": "10126", "name": "قطرات الذهب", "company": "ese", "price": 3573.00},
        {"code": "9755", "name": "كوكو نت", "company": "ese", "price": 1791.00},
        {"code": "104E", "name": "لافندر", "company": "ese", "price": 1935.00},
        {"code": "117E", "name": "لافندر سوبر", "company": "ese", "price": 2421.00},
        {"code": "70145", "name": "مخلط البخور", "company": "ese", "price": 2970.00},
        {"code": "703", "name": "مذهله عود كلمات)", "company": "ese", "price": 3582.00},
        {"code": "70001", "name": "مسك الاميرات", "company": "ese", "price": 3270.00},
        {"code": "132E", "name": "مسك ابيض", "company": "ese", "price": 2133.00},
        {"code": "97576", "name": "مسك الجسم", "company": "ese", "price": 3231.00},
        {"code": "R43", "name": "مسك الطهارة", "company": "ese", "price": 3361.00},
        {"code": "97543", "name": "مسك الملوك", "company": "ese", "price": 4473.00},
        {"code": "97507", "name": "مسك مكة", "company": "ese", "price": 2421.00},
        {"code": "10107", "name": "مسك مكة (نخب اول)", "company": "ese", "price": 2900.00},
        {"code": "7003", "name": "مسك متسلق", "company": "ese", "price": 3470.00},
        {"code": "97532", "name": "مضاوي", "company": "ese", "price": 3501.00},
        {"code": "7511", "name": "اسكيب نخب اول", "company": "ese", "price": 2601.00},
        {"code": "10115", "name": "بنك شوجر", "company": "ese", "price": 1790.00},
        {"code": "10129", "name": "خمرة - لطافة", "company": "ese", "price": 1970.00},
        {"code": "6002", "name": "ازارو ونتد نخب اول", "company": "ese", "price": 4790.00},
        {"code": "573", "name": "استرونجر انتسلى", "company": "ese", "price": 3970.00},
        {"code": "509", "name": "جواد الليل (نخب اول)", "company": "ese", "price": 5970.00},
        {"code": "539", "name": "کرید افنتوس (نخب اول)", "company": "ese", "price": 15790.00},
        {"code": "176", "name": "سوفاج اكسترا", "company": "ese", "price": 3951.00},
        {"code": "170", "name": "سویت بیری", "company": "ese", "price": 3270.00},
        {"code": "563", "name": "يارا کاندی (نخب اول)", "company": "ese", "price": 3975.00},
        
        # ارجفيل
        {"code": "33", "name": "فكتوريا سكريت", "company": "ارجفيل", "price": 3450.00},
        {"code": "22344", "name": "بريتني سبيرز", "company": "ارجفيل", "price": 2300.00},
        {"code": "31592", "name": "بريتني سبيرز", "company": "ارجفيل", "price": 3150.00},
        {"code": "57950", "name": "سلفر سنت 2000", "company": "ارجفيل", "price": 4175.00},
        {"code": "38955", "name": "دوف", "company": "ارجفيل", "price": 2075.00},
        {"code": "2372", "name": "امير الشباب", "company": "ارجفيل", "price": 3225.00},
        {"code": "26194", "name": "فانليا", "company": "ارجفيل", "price": 2100.00},
        {"code": "56915", "name": "سوفاج", "company": "ارجفيل", "price": 3925.00},
        {"code": "43473", "name": "لاف ذا هفنلی 2000", "company": "ارجفيل", "price": 4075.00},
        
        # ذا میش
        {"code": "3468", "name": "فوياج", "company": "ذا میش", "price": 3570.00},
        {"code": "3585", "name": "فرى سكسى سي", "company": "ذا میش", "price": 4030.00},
        {"code": "3447", "name": "فرى سكسى اوركيد", "company": "ذا میش", "price": 3550.00},
        {"code": "2200M", "name": "اربا بورا", "company": "ذا میش", "price": 5900.00},
        {"code": "2250", "name": "اسكلبشر", "company": "ذا میش", "price": 3070.00},
        {"code": "2376", "name": "انجل نوفا", "company": "ذا میش", "price": 3020.00},
        {"code": "3441", "name": "باريس هيلتون", "company": "ذا میش", "price": 3370.00},
        {"code": "3426", "name": "بربری هیر حریمی", "company": "ذا میش", "price": 3650.00},
        {"code": "2517", "name": "بريتني برينسز", "company": "ذا میش", "price": 3550.00},
        {"code": "2220M", "name": "بكرات دفانا", "company": "ذا میش", "price": 4290.00},
        {"code": "2256", "name": "بلاك ليكزس", "company": "ذا میش", "price": 3540.00},
        {"code": "3429", "name": "بلو شانيل", "company": "ذا میش", "price": 4250.00},
        {"code": "2526", "name": "بينك شوجر", "company": "ذا میش", "price": 2670.00},
        {"code": "2343", "name": "جوتچی رش", "company": "ذا میش", "price": 3490.00},
        {"code": "3438", "name": "جود جيرل", "company": "ذا میش", "price": 3670.00},
        {"code": "3423", "name": "دانهیل دیزایر", "company": "ذا میش", "price": 3600.00},
        {"code": "2541", "name": "دولسن اند جابانا 3", "company": "ذا میش", "price": 3780.00},
        {"code": "3288", "name": "راش", "company": "ذا میش", "price": 2700.00},
        {"code": "2595", "name": "روز فانيليا", "company": "ذا میش", "price": 3700.00},
        {"code": "2238", "name": "سوسبيرو اكسنتوا", "company": "ذا میش", "price": 12600.00},
        {"code": "2559", "name": "سوفاج", "company": "ذا میش", "price": 3920.00},
        {"code": "3420", "name": "شالیز مان", "company": "ذا میش", "price": 3770.00},
        {"code": "2298", "name": "شامبيون", "company": "ذا میش", "price": 3770.00},
        {"code": "2310", "name": "عنبر الملوك", "company": "ذا میش", "price": 4990.00},
        {"code": "2340", "name": "عود عمانی", "company": "ذا میش", "price": 3880.00},
        {"code": "3435", "name": "لاف ذا هفنلی", "company": "ذا میش", "price": 3190.00},
        {"code": "2208", "name": "لافي بيل", "company": "ذا میش", "price": 3550.00},
        {"code": "2466", "name": "لاكوست استنشال", "company": "ذا میش", "price": 3470.00},
        {"code": "2487", "name": "ليل ذهبي", "company": "ذا میش", "price": 2920.00},
        {"code": "2481", "name": "ليل ساطع", "company": "ذا میش", "price": 3590.00},
        {"code": "2478", "name": "مسك الكعبة", "company": "ذا میش", "price": 3870.00},
        {"code": "3444", "name": "مضاوى جولد", "company": "ذا میش", "price": 6200.00},
        {"code": "3432", "name": "مون سباركل", "company": "ذا میش", "price": 2970.00},
        {"code": "2535", "name": "هريرا 212", "company": "ذا میش", "price": 3500.00},
        {"code": "2316", "name": "وصال", "company": "ذا میش", "price": 4270.00},
        {"code": "2580", "name": "بلو فور مان", "company": "ذا میش", "price": 3770.00},
        {"code": "2475", "name": "دولسی جابانا رد", "company": "ذا میش", "price": 3800.00},
        {"code": "2349", "name": "استرونجر ويذ يو ليذر", "company": "ذا میش", "price": 4290.00},
        {"code": "3591", "name": "ایمانویل", "company": "ذا میش", "price": 4880.00},
        {"code": "3777", "name": "نيو بوص", "company": "ذا میش", "price": 2950.00},
        {"code": "3783", "name": "نينا ناتشر", "company": "ذا میش", "price": 2900.00},
        {"code": "3765", "name": "كريد سلفر", "company": "ذا میش", "price": 4380.00},
        {"code": "3741", "name": "استرونجر ويز يو", "company": "ذا میش", "price": 3290.00},
        {"code": "3771", "name": "بلو جينز", "company": "ذا میش", "price": 3380.00},
        {"code": "2493", "name": "انفكتوس روج", "company": "ذا میش", "price": 4190.00},
        {"code": "2529", "name": "کریزی لاف", "company": "ذا میش", "price": 2590.00},
        {"code": "3894", "name": "سوفكتوس", "company": "ذا میش", "price": 4500.00},
        {"code": "2361", "name": "انجل شیر", "company": "ذا میش", "price": 7750.00},
        {"code": "2985", "name": "مسك الطهاره", "company": "ذا میش", "price": 4020.00},
        {"code": "4050", "name": "بيانكو لاتيه", "company": "ذا میش", "price": 3000.00},
        {"code": "3465", "name": "کرید افنتوس", "company": "ذا میش", "price": 9970.00},
        {"code": "3601", "name": "بلو شانيل 2000", "company": "ذا میش", "price": 4300.00},
        
        # Eip
        {"code": "40509", "name": "ليبر ايف سان لوران", "company": "Eip", "price": 6230.00},
        {"code": "3717", "name": "سی فیوری", "company": "Eip", "price": 2990.00},
        {"code": "2527", "name": "عود شيخه", "company": "Eip", "price": 2900.00},
        {"code": "7937", "name": "سی ارمانی", "company": "Eip", "price": 2800.00},
        {"code": "5268", "name": "كريد بلاك", "company": "Eip", "price": 3900.00},
        {"code": "40505", "name": "کی دولسی جابانا", "company": "Eip", "price": 3300.00},
        {"code": "412", "name": "بلاك اكس اس بوشن", "company": "Eip", "price": 2550.00},
        {"code": "3907", "name": "هيرمز 24 ساعه", "company": "Eip", "price": 2400.00},
        {"code": "835", "name": "لوس انجلوس", "company": "Eip", "price": 2600.00},
        {"code": "832", "name": "بربری رجالی", "company": "Eip", "price": 2400.00},
        {"code": "10011", "name": "روبی نوار", "company": "Eip", "price": 1500.00},
    ]
    
    with app.app_context():
        try:
            # إنشاء أو الحصول على الموردين الجدد
            companies = set([item["company"] for item in perfumes_data])
            suppliers = {}
            
            for company_name in companies:
                supplier = Supplier.query.filter_by(name=company_name).first()
                if not supplier:
                    supplier = Supplier(
                        name=company_name,
                        contact_person="غير محدد",
                        phone="غير محدد",
                        address="غير محدد"
                    )
                    db.session.add(supplier)
                    db.session.flush()
                suppliers[company_name] = supplier
            
            # إضافة العطور
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = suppliers[perfume_data["company"]]
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - {perfume_data['company']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=suppliers[perfume_data["company"]],
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - {perfume_data['company']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")
            print(f"📊 إجمالي العطور المعالجة: {len(perfumes_data)}")
            print(f"🏢 عدد الموردين الجدد: {len(companies)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد باقي البيانات...")
    print("=" * 70)
    
    success = import_remaining_data()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("🌟 تم إكمال قاعدة البيانات النهائية!")
        print("يمكنك الآن تشغيل النظام ومراجعة جميع العطور.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
