#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام إدارة العطور
ملف التشغيل الرئيسي
"""

import os
import sys

# إضافة مجلد backend للمسار
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from backend.app import app

    if __name__ == '__main__':
        print("Starting Perfume Store Management System...")
        print("System running on: http://127.0.0.1:5000")
        print("Default user: ammar / ammar")
        print("=" * 50)

        # Run the application
        app.run(debug=True, host='127.0.0.1', port=5000)

except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all dependencies are installed")
    print("Run: pip install Flask Flask-Login Flask-SQLAlchemy pandas reportlab xlsxwriter")
    input("Press Enter to exit...")
    sys.exit(1)
except Exception as e:
    print(f"Application error: {e}")
    input("Press Enter to exit...")
    sys.exit(1)
