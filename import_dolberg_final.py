#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد المجموعة الأخيرة من عطور ومنتجات دولبرج
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_dolberg_final():
    """استيراد المجموعة الأخيرة من منتجات دولبرج"""
    
    # بيانات المنتجات الجديدة
    perfumes_data = [
        # العطور المتبقية
        {"code": "9970", "name": "كوكو مدموازيل", "company": "دولبرج", "price": 3290.00},
        {"code": "8998", "name": "كول ووتر بلو", "company": "دولبرج", "price": 2690.00},
        {"code": "7952", "name": "كول ووتر بلو", "company": "دولبرج", "price": 3590.00},
        {"code": "9955", "name": "كيركي", "company": "دولبرج", "price": 8150.00},
        {"code": "9909", "name": "لؤلؤة البحرين", "company": "دولبرج", "price": 4750.00},
        {"code": "9939", "name": "لا برفيوم", "company": "دولبرج", "price": 3450.00},
        {"code": "9942", "name": "لا برفيوم إن وايت", "company": "دولبرج", "price": 3750.00},
        {"code": "9952", "name": "لا برفيوم رويال", "company": "دولبرج", "price": 4325.00},
        {"code": "9919", "name": "لابيدوس", "company": "دولبرج", "price": 2875.00},
        {"code": "7979", "name": "لابيدوس", "company": "دولبرج", "price": 5300.00},
        {"code": "9900", "name": "لابيدوس", "company": "دولبرج", "price": 4100.00},
        {"code": "9929", "name": "لاف إذ هفنلي", "company": "دولبرج", "price": 3940.00},
        {"code": "9949", "name": "لا في بيل إن روز", "company": "دولبرج", "price": 3750.00},
        {"code": "9978", "name": "لا في بيل", "company": "دولبرج", "price": 4190.00},
        {"code": "5914", "name": "لاكوست اسنشيال", "company": "دولبرج", "price": 2500.00},
        {"code": "8968", "name": "لاكوست اسنشيال", "company": "دولبرج", "price": 3100.00},
        {"code": "8931", "name": "لاكوست بلاك", "company": "دولبرج", "price": 4150.00},
        {"code": "7947", "name": "لاكوست تشالينج", "company": "دولبرج", "price": 4300.00},
        {"code": "8972", "name": "لاكوست وايت", "company": "دولبرج", "price": 3175.00},
        {"code": "7906", "name": "ليدي مليون", "company": "دولبرج", "price": 2875.00},
        {"code": "8956", "name": "ماجي نوار", "company": "دولبرج", "price": 2800.00},
        {"code": "5909", "name": "ماربرت مان", "company": "دولبرج", "price": 2500.00},
        {"code": "7969", "name": "ماربرت مان", "company": "دولبرج", "price": 3940.00},
        {"code": "8935", "name": "مخلط عود", "company": "دولبرج", "price": 5800.00},
        {"code": "8925", "name": "مسك أبيض", "company": "دولبرج", "price": 3280.00},
        {"code": "5916", "name": "مسك إنجليزي", "company": "دولبرج", "price": 2475.00},
        {"code": "8965", "name": "مسك إنجليزي", "company": "دولبرج", "price": 3950.00},
        {"code": "9994", "name": "مسك رمان", "company": "دولبرج", "price": 6500.00},
        {"code": "8957", "name": "مسك سعودي", "company": "دولبرج", "price": 2875.00},
        {"code": "8932", "name": "مسك عجايبي", "company": "دولبرج", "price": 2890.00},
        {"code": "7994", "name": "مسك عربي", "company": "دولبرج", "price": 3280.00},
        {"code": "9985", "name": "مضاوي", "company": "دولبرج", "price": 3590.00},
        {"code": "9986", "name": "مضاوي", "company": "دولبرج", "price": 6990.00},
        {"code": "8963", "name": "مون بلو ليجند", "company": "دولبرج", "price": 3200.00},
        {"code": "7985", "name": "مونتانا", "company": "دولبرج", "price": 2815.00},
        {"code": "9999", "name": "مید نایت", "company": "دولبرج", "price": 2395.00},
        {"code": "8959", "name": "مید نایت", "company": "دولبرج", "price": 2825.00},
        {"code": "9960", "name": "میموریز", "company": "دولبرج", "price": 3935.00},
        {"code": "5921", "name": "ناو الأسود (جديد)", "company": "دولبرج", "price": 5750.00},
        {"code": "9922", "name": "نرسیسو روج", "company": "دولبرج", "price": 3750.00},
        {"code": "9905", "name": "نساء العالم", "company": "دولبرج", "price": 3325.00},
        {"code": "8922", "name": "هاواي", "company": "دولبرج", "price": 3050.00},
        {"code": "8921", "name": "هريرا 212 VIP", "company": "دولبرج", "price": 3075.00},
        {"code": "8920", "name": "هريرا 212 VIP", "company": "دولبرج", "price": 3750.00},
        {"code": "8976", "name": "هريرا 212 VIP", "company": "دولبرج", "price": 4190.00},
        {"code": "9934", "name": "هريرا 212 سكسي رجالي", "company": "دولبرج", "price": 3175.00},
        {"code": "9941", "name": "هريرا 212 سكسي حريمي", "company": "دولبرج", "price": 3695.00},
        {"code": "8995", "name": "هوجو", "company": "دولبرج", "price": 2425.00},
        {"code": "7918", "name": "هوجو", "company": "دولبرج", "price": 3375.00},
        {"code": "5926", "name": "هوجو بوتلد باسيفيك (جديد)", "company": "دولبرج", "price": 4600.00},
        {"code": "9989", "name": "هيروس فوريفر", "company": "دولبرج", "price": 3690.00},
        {"code": "9984", "name": "هيلين", "company": "دولبرج", "price": 4590.00},
        {"code": "8980", "name": "وان مان شو", "company": "دولبرج", "price": 2680.00},
        {"code": "7999", "name": "وان مان شو", "company": "دولبرج", "price": 5550.00},
        {"code": "8981", "name": "وان مليون", "company": "دولبرج", "price": 2825.00},
        {"code": "7941", "name": "وان مليون", "company": "دولبرج", "price": 3695.00},
        {"code": "8982", "name": "ورد بلدی", "company": "دولبرج", "price": 1900.00},
        {"code": "7922", "name": "ورد بلدی", "company": "دولبرج", "price": 3250.00},
        {"code": "9992", "name": "وصال", "company": "دولبرج", "price": 2995.00},
        {"code": "7936", "name": "وصال", "company": "دولبرج", "price": 4790.00},
        {"code": "7916", "name": "ويك إند", "company": "دولبرج", "price": 3260.00},
        {"code": "5935", "name": "يارا (جديد)", "company": "دولبرج", "price": 4950.00},
        {"code": "5938B", "name": "BMW", "company": "دولبرج", "price": 3650.00},
        {"code": "5940", "name": "تراب الذهب", "company": "دولبرج", "price": 3250.00},
        {"code": "5941", "name": "ناركوتيك ديلايت", "company": "دولبرج", "price": 5950.00},
        {"code": "5942", "name": "سی باشون", "company": "دولبرج", "price": 4575.00},
        {"code": "5944", "name": "كوكو فانيليا", "company": "دولبرج", "price": 3950.00},
        {"code": "5943", "name": "انجل نوفا", "company": "دولبرج", "price": 3250.00},
        {"code": "5946", "name": "روز فانيليا", "company": "دولبرج", "price": 3450.00},
        {"code": "5947", "name": "هاشيفات", "company": "دولبرج", "price": 3500.00},  # سعر افتراضي
        {"code": "5948", "name": "لايتون (دی مارلی)", "company": "دولبرج", "price": 5400.00},
        {"code": "5949", "name": "الوسام", "company": "دولبرج", "price": 4950.00},
        {"code": "5951", "name": "بلاك اوبيوم اوفر ريد", "company": "دولبرج", "price": 4890.00},
        {"code": "5952", "name": "وای ایف سان لوران", "company": "دولبرج", "price": 3550.00},
        {"code": "5954", "name": "لاف ذا هفنلي", "company": "دولبرج", "price": 4500.00},
        {"code": "5955", "name": "اسكندال بور هوم", "company": "دولبرج", "price": 3850.00},
        {"code": "5956", "name": "ليبر ايف سان لوران", "company": "دولبرج", "price": 3650.00},
        {"code": "5957", "name": "انفكتوس فكتوري", "company": "دولبرج", "price": 4590.00},
        {"code": "5961", "name": "اربا بورا 2", "company": "دولبرج", "price": 4990.00},
        
        # المنتجات الطبيعية والصناعية
        {"code": "1001", "name": "خوخ (صناعي)", "company": "دولبرج", "price": 1400.00},
        {"code": "1002", "name": "ياسمين", "company": "دولبرج", "price": 1250.00},
        {"code": "1003", "name": "ورد", "company": "دولبرج", "price": 1250.00},
        {"code": "1004", "name": "برتقال (صناعي)", "company": "دولبرج", "price": 1250.00},
        {"code": "1005", "name": "ليمون", "company": "دولبرج", "price": 1600.00},
        {"code": "1006", "name": "فراولة (صناعي)", "company": "دولبرج", "price": 1250.00},
        {"code": "1007", "name": "أناناس", "company": "دولبرج", "price": 1500.00},
        {"code": "1008", "name": "توت بري", "company": "دولبرج", "price": 1500.00},
        {"code": "1009", "name": "تفاح أخضر (صناعي)", "company": "دولبرج", "price": 1600.00},
        {"code": "1010", "name": "تفاح (صناعي)", "company": "دولبرج", "price": 1600.00},
        {"code": "1011", "name": "فواكه إستوائية", "company": "دولبرج", "price": 1990.00},
        {"code": "1012", "name": "مانجو", "company": "دولبرج", "price": 1600.00},
        {"code": "1013", "name": "كيوي", "company": "دولبرج", "price": 1675.00},
        {"code": "1014", "name": "جارنييه فراكتس كوكو واتر", "company": "دولبرج", "price": 3125.00},
        {"code": "1015", "name": "جارنييه فراكتس براکت بوست", "company": "دولبرج", "price": 3150.00},
        {"code": "1016", "name": "جودباي داميدج", "company": "دولبرج", "price": 3200.00},
        {"code": "1017", "name": "بانتين برو - في", "company": "دولبرج", "price": 3125.00},
        {"code": "1018", "name": "لوريال بلانتا كلير", "company": "دولبرج", "price": 3325.00},
        {"code": "1019", "name": "ليلاك (صناعي)", "company": "دولبرج", "price": 1500.00},
    ]
    
    with app.app_context():
        try:
            # الحصول على مورد دولبرج
            supplier = Supplier.query.filter_by(name="دولبرج").first()
            if not supplier:
                supplier = Supplier(
                    name="دولبرج",
                    contact_person="غير محدد",
                    phone="غير محدد",
                    address="غير محدد"
                )
                db.session.add(supplier)
                db.session.flush()
            
            # إضافة المنتجات
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان المنتج موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = supplier
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - كود: {perfume_data['code']}")
                else:
                    # إضافة منتج جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=supplier,
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - كود: {perfume_data['code']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد المنتجات المضافة: {added_count}")
            print(f"🔄 عدد المنتجات المحدثة: {updated_count}")
            print(f"📊 إجمالي المنتجات المعالجة: {len(perfumes_data)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد المجموعة الأخيرة من منتجات دولبرج...")
    print("=" * 70)
    
    success = import_dolberg_final()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("🌟 تم إكمال قاعدة بيانات دولبرج الكاملة!")
        print("يمكنك الآن تشغيل النظام ومراجعة جميع المنتجات.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
