<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .form-card { max-width: 600px; margin: 0 auto; }
        .required { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>
<div class="container">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card shadow form-card">
        <div class="card-header bg-primary text-white text-center">
          <h4 class="mb-0">
            <i class="bi bi-{% if perfume %}pencil{% else %}plus-circle{% endif %}"></i>
            {{ title }}
          </h4>
        </div>
        <div class="card-body">
          <!-- Flash Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }} alert-dismissible fade show">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
              {% endfor %}
            {% endif %}
          {% endwith %}

          <form method="post" novalidate>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-tag"></i> اسم العطر <span class="required">*</span>
                </label>
                <input type="text" name="name" class="form-control" required
                       value="{{ perfume.name if perfume else '' }}"
                       placeholder="أدخل اسم العطر">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-building"></i> اسم الشركة <span class="required">*</span>
                </label>
                <input type="text" name="company" class="form-control" required
                       value="{{ perfume.company if perfume else '' }}"
                       placeholder="أدخل اسم الشركة المصنعة">
              </div>
            </div>

            <!-- نوع البيع -->
            <div class="row">
              <div class="col-md-12 mb-3">
                <label class="form-label">
                  <i class="bi bi-gear"></i> نوع البيع <span class="required">*</span>
                </label>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="sale_type" id="sale_by_piece" value="piece"
                         {% if not perfume or not perfume.is_sold_by_weight %}checked{% endif %}
                         onchange="toggleSaleType()">
                  <label class="form-check-label" for="sale_by_piece">
                    <i class="bi bi-box"></i> بيع بالقطعة
                  </label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="sale_type" id="sale_by_weight" value="weight"
                         {% if perfume and perfume.is_sold_by_weight %}checked{% endif %}
                         onchange="toggleSaleType()">
                  <label class="form-check-label" for="sale_by_weight">
                    <i class="bi bi-speedometer2"></i> بيع بالوزن
                  </label>
                </div>
              </div>
            </div>

            <!-- أسعار البيع بالقطعة -->
            <div id="piece_pricing" class="row" style="display: {% if perfume and perfume.is_sold_by_weight %}none{% else %}block{% endif %};">
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-currency-dollar"></i> السعر للقطعة (ج.م) <span class="required">*</span>
                </label>
                <input type="number" name="price" class="form-control"
                       step="0.01" min="0"
                       value="{{ perfume.price if perfume and not perfume.is_sold_by_weight else '' }}"
                       placeholder="0.00">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-box"></i> الكمية المتاحة (قطعة) <span class="text-muted">(اختياري)</span>
                </label>
                <input type="number" name="quantity" class="form-control"
                       min="0"
                       value="{{ perfume.quantity if perfume else '999999' }}"
                       placeholder="999999">
                <small class="text-muted">اتركها كما هي للكمية المفتوحة</small>
              </div>
            </div>

            <!-- أسعار البيع بالوزن -->
            <div id="weight_pricing" class="row" style="display: {% if perfume and perfume.is_sold_by_weight %}block{% else %}none{% endif %};">
              <div class="col-md-4 mb-3">
                <label class="form-label">
                  <i class="bi bi-currency-dollar"></i> سعر الكيلو (ج.م) <span class="required">*</span>
                </label>
                <input type="number" name="price_per_kg" class="form-control"
                       step="0.01" min="0"
                       value="{{ perfume.price_per_kg if perfume and perfume.is_sold_by_weight else '' }}"
                       placeholder="0.00"
                       onchange="calculateGramPrice()">
              </div>
              <div class="col-md-4 mb-3">
                <label class="form-label">
                  <i class="bi bi-speedometer2"></i> سعر الجرام (ج.م)
                </label>
                <input type="text" id="price_per_gram" class="form-control" readonly
                       placeholder="يحسب تلقائياً">
              </div>
              <div class="col-md-4 mb-3">
                <label class="form-label">
                  <i class="bi bi-box"></i> الكمية المتاحة (كيلو) <span class="text-muted">(اختياري)</span>
                </label>
                <input type="number" name="quantity_kg" class="form-control"
                       step="0.001" min="0"
                       value="{{ (perfume.quantity / 1000) if perfume and perfume.is_sold_by_weight else '999.999' }}"
                       placeholder="999.999">
                <small class="text-muted">اتركها كما هي للكمية المفتوحة</small>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-exclamation-triangle"></i> الحد الأدنى للتنبيه <span class="text-muted">(اختياري)</span>
                </label>
                <input type="number" name="min_quantity" class="form-control"
                       min="0"
                       value="{{ perfume.min_quantity if perfume else 0 }}"
                       placeholder="0">
                <small class="text-muted">0 = بدون تنبيهات</small>
              </div>
            </div>

            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-success btn-lg">
                <i class="bi bi-save"></i> حفظ البيانات
              </button>
              <a href="/products" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> العودة لقائمة العطور
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
function toggleSaleType() {
    const saleByPiece = document.getElementById('sale_by_piece').checked;
    const piecePricing = document.getElementById('piece_pricing');
    const weightPricing = document.getElementById('weight_pricing');

    if (saleByPiece) {
        piecePricing.style.display = 'block';
        weightPricing.style.display = 'none';

        // إزالة required من حقول الوزن
        weightPricing.querySelectorAll('input').forEach(input => {
            input.removeAttribute('required');
        });

        // إضافة required لحقول القطعة
        piecePricing.querySelectorAll('input').forEach(input => {
            if (input.name === 'price' || input.name === 'quantity') {
                input.setAttribute('required', 'required');
            }
        });
    } else {
        piecePricing.style.display = 'none';
        weightPricing.style.display = 'block';

        // إزالة required من حقول القطعة
        piecePricing.querySelectorAll('input').forEach(input => {
            input.removeAttribute('required');
        });

        // إضافة required لحقول الوزن
        weightPricing.querySelectorAll('input').forEach(input => {
            if (input.name === 'price_per_kg' || input.name === 'quantity_kg') {
                input.setAttribute('required', 'required');
            }
        });

        // حساب سعر الجرام
        calculateGramPrice();
    }
}

function calculateGramPrice() {
    const pricePerKg = parseFloat(document.querySelector('input[name="price_per_kg"]').value) || 0;
    const pricePerGram = pricePerKg / 1000;
    document.getElementById('price_per_gram').value = pricePerGram.toFixed(3) + ' ج.م';
}

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleSaleType();
    calculateGramPrice();
});
</script>

</body>
</html>