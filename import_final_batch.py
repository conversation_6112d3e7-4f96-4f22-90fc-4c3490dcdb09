#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد المجموعة الأخيرة من العطور
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_final_batch():
    """استيراد المجموعة الأخيرة من العطور"""
    
    # بيانات العطور الأخيرة
    perfumes_data = [
        # Apa - المجموعة المتبقية
        {"code": "1093", "name": "جورج قرداحی", "company": "Apa", "price": 2145.00},
        {"code": "7014", "name": "دهن عود ابيض", "company": "Apa", "price": 6830.00},
        {"code": "6080", "name": "رومبا", "company": "Apa", "price": 2865.00},
        {"code": "2080", "name": "رومبا", "company": "Apa", "price": 1810.00},
        {"code": "1104", "name": "سوفاج", "company": "Apa", "price": 2560.00},
        {"code": "1020A", "name": "سيلفر سنت 1000", "company": "Apa", "price": 2430.00},
        {"code": "4020", "name": "سيلفر سنت 2000", "company": "Apa", "price": 3470.00},
        {"code": "8820", "name": "سيلفر سنت 3000", "company": "Apa", "price": 4610.00},
        {"code": "1062", "name": "شانيل بلو", "company": "Apa", "price": 3000.00},
        {"code": "7025", "name": "عود مبخر", "company": "Apa", "price": 5640.00},
        {"code": "2020", "name": "فانتازيا", "company": "Apa", "price": 2650.00},
        {"code": "7000A", "name": "فل", "company": "Apa", "price": 1470.00},
        {"code": "4018", "name": "في اي بي", "company": "Apa", "price": 3105.00},
        {"code": "2065", "name": "في اي بي حريمي", "company": "Apa", "price": 2350.00},
        {"code": "3325", "name": "كاسيليا", "company": "Apa", "price": 2605.00},
        {"code": "6026", "name": "کریزی لاف", "company": "Apa", "price": 2265.00},
        {"code": "7018", "name": "كلك اخضر", "company": "Apa", "price": 2195.00},
        {"code": "7017", "name": "كلك اصفر", "company": "Apa", "price": 2195.00},
        {"code": "4024", "name": "کنزو 2000", "company": "Apa", "price": 2940.00},
        {"code": "2048", "name": "لافی ایبل", "company": "Apa", "price": 2865.00},
        {"code": "1033", "name": "لاكوست استنشال", "company": "Apa", "price": 2630.00},
        {"code": "4013", "name": "لاكوست وايت", "company": "Apa", "price": 2760.00},
        {"code": "2021", "name": "ليدى مليون", "company": "Apa", "price": 2400.00},
        {"code": "1051", "name": "وان مليون", "company": "Apa", "price": 2495.00},
        {"code": "1092", "name": "ماربت مان 1000", "company": "Apa", "price": 2400.00},
        {"code": "4025", "name": "ماربت مان 2000", "company": "Apa", "price": 3370.00},
        {"code": "2068", "name": "مون سباركل", "company": "Apa", "price": 2400.00},
        {"code": "2003", "name": "مید نایت", "company": "Apa", "price": 2495.00},
        {"code": "1023A", "name": "هريرا 212", "company": "Apa", "price": 2865.00},
        {"code": "4023", "name": "هريرا 212", "company": "Apa", "price": 3800.00},
        {"code": "1055", "name": "هوجو", "company": "Apa", "price": 2325.00},
        {"code": "1006", "name": "هوجو بلاك", "company": "Apa", "price": 2915.00},
        {"code": "1095", "name": "ايفوريا رجالي", "company": "Apa", "price": 2650.00},
        {"code": "4000", "name": "عمرو دياب", "company": "Apa", "price": 3615.00},
        {"code": "1049", "name": "فيندي", "company": "Apa", "price": 2470.00},
        {"code": "1068", "name": "جان بول", "company": "Apa", "price": 2865.00},
        {"code": "2081", "name": "کنزو فلاور", "company": "Apa", "price": 2325.00},
        {"code": "7015A", "name": "وصال", "company": "Apa", "price": 2720.00},
        {"code": "1067", "name": "لاكوست بلاك", "company": "Apa", "price": 2705.00},
        {"code": "2033", "name": "كول ووتر بلو", "company": "Apa", "price": 2400.00},
        {"code": "1080", "name": "دیزایر بلو", "company": "Apa", "price": 2495.00},
        {"code": "1109", "name": "باد بوى (كارولينا هريرا)", "company": "Apa", "price": 3675.00},
        {"code": "4447", "name": "بلاك ليكسز", "company": "Apa", "price": 2950.00},
        {"code": "4497", "name": "انفكتوس", "company": "Apa", "price": 3320.00},
        {"code": "8810", "name": "شيروتي", "company": "Apa", "price": 4285.00},
        {"code": "8819", "name": "جوب", "company": "Apa", "price": 3530.00},
        {"code": "8880", "name": "دیزایر بلو", "company": "Apa", "price": 5435.00},
        {"code": "9900", "name": "ويك اند", "company": "Apa", "price": 4150.00},
        {"code": "2222", "name": "بيانكو لاتيه", "company": "Apa", "price": 3000.00},
        
        # ipra
        {"code": "14222", "name": "اليرسبورت", "company": "ipra", "price": 3735.00},
        {"code": "14242", "name": "فخر بلاك", "company": "ipra", "price": 4545.00},
        {"code": "26011", "name": "اوليمبيا", "company": "ipra", "price": 5590.00},
        {"code": "55002", "name": "بكرات روج", "company": "ipra", "price": 7630.00},
        {"code": "55025", "name": "سموكينج هوت", "company": "ipra", "price": 8100.00},
        {"code": "55027", "name": "الكسندريا 2 زيرجوف", "company": "ipra", "price": 9100.00},
        {"code": "77119", "name": "ليل ملكي", "company": "ipra", "price": 5370.00},
        {"code": "77214", "name": "عود وود", "company": "ipra", "price": 8580.00},
        {"code": "22126", "name": "ليبر ايف سان لوران", "company": "ipra", "price": 3000.00},
        {"code": "22283", "name": "سمر ای امور", "company": "ipra", "price": 2900.00},
        {"code": "22286", "name": "برازلیان کراش شبروزا", "company": "ipra", "price": 2700.00},
        {"code": "11206", "name": "جنتل مان سوسایتی", "company": "ipra", "price": 2940.00},
        {"code": "77224", "name": "كوكو فانيليا طهاره", "company": "ipra", "price": 2800.00},
        {"code": "77281", "name": "بربي هير مسك اخضر", "company": "ipra", "price": 3400.00},
        {"code": "22261", "name": "جينجهام باث اند بادی", "company": "ipra", "price": 2150.00},
        {"code": "22262", "name": "جينجهام لاف باث اند بادی", "company": "ipra", "price": 3000.00},
        {"code": "22263", "name": "فراوله كيك باث اند بادی", "company": "ipra", "price": 2850.00},
        {"code": "55035", "name": "لوست شیری توم فورد", "company": "ipra", "price": 4600.00},
        {"code": "77305", "name": "اربيان تونكا", "company": "ipra", "price": 5300.00},
        {"code": "55070", "name": "دالاتشای مونتال", "company": "ipra", "price": 5600.00},
        {"code": "55060", "name": "ارابيانز مسك", "company": "ipra", "price": 4350.00},
        {"code": "55075", "name": "عود ماراكوجا", "company": "ipra", "price": 19500.00},
        {"code": "77399", "name": "بلاك افغانوا", "company": "ipra", "price": 5500.00},
        {"code": "77320", "name": "ساید ایفکت", "company": "ipra", "price": 6380.00},
        {"code": "77024", "name": "کشمیر", "company": "ipra", "price": 2560.00},
        {"code": "14320", "name": "استرونجر ويز يو برفيوم", "company": "ipra", "price": 4300.00},
        {"code": "22265", "name": "بيور واندر باث اند بادی", "company": "ipra", "price": 2900.00},
        {"code": "22266", "name": "دارك كيس باث اند بادی", "company": "ipra", "price": 2800.00},
        {"code": "22269", "name": "جينجهام جلو باث اند بادی", "company": "ipra", "price": 2900.00},
        {"code": "22268", "name": "جينجهام جورجيوس باث اند بادی", "company": "ipra", "price": 2500.00},
        {"code": "22261F", "name": "جينجهام لاف F باث اند بادی", "company": "ipra", "price": 2150.00},
        {"code": "22270", "name": "دارك فلفيت عود باث اند بادی", "company": "ipra", "price": 2600.00},
        {"code": "22259", "name": "ان تو ذا نایت باث اند بادی", "company": "ipra", "price": 2500.00},
        {"code": "22292", "name": "اجود بينك تو بينك (لطافة)", "company": "ipra", "price": 2500.00},
        {"code": "22267", "name": "جينجهام فریش باث اند بادی", "company": "ipra", "price": 2500.00},
        {"code": "77350", "name": "اربيان توباكو (القرشي)", "company": "ipra", "price": 4200.00},
        {"code": "11290", "name": "اولد فاشون (کیلیان)", "company": "ipra", "price": 2900.00},
        {"code": "14271", "name": "اسد بوریون", "company": "ipra", "price": 4400.00},
        {"code": "14324", "name": "استرونجر ويز يو صندل", "company": "ipra", "price": 4500.00},
        {"code": "55080", "name": "انجل شیر بارادايس", "company": "ipra", "price": 5700.00},
        {"code": "77332", "name": "امير العرب (اصداف)", "company": "ipra", "price": 4400.00},
        {"code": "77361", "name": "خمرة دخان (لطافه)", "company": "ipra", "price": 4400.00},
        {"code": "77254", "name": "خمرة (لطافه)", "company": "ipra", "price": 4500.00},
        {"code": "14245", "name": "استرونجر ویزیو ابسولوتلی", "company": "ipra", "price": 4190.00},
        
        # جولدن / أبرشيم
        {"code": "3111", "name": "BMW", "company": "جولدن / أبرشيم", "price": 3750.00},
        {"code": "24605", "name": "ابراهيمو فيتش", "company": "جولدن / أبرشيم", "price": 4000.00},
        {"code": "8571", "name": "احساس (العربية للعود)", "company": "جولدن / أبرشيم", "price": 5040.00},
        {"code": "83717", "name": "اربا بورا", "company": "جولدن / أبرشيم", "price": 6750.00},
        {"code": "423", "name": "اربا بورا (منتج طبيعي)", "company": "جولدن / أبرشيم", "price": 16500.00},
        {"code": "8040", "name": "ازارو ونتد", "company": "جولدن / أبرشيم", "price": 4290.00},
        {"code": "61330", "name": "استرونجر عود", "company": "جولدن / أبرشيم", "price": 4280.00},
        {"code": "3147", "name": "استرونجر ويز يو", "company": "جولدن / أبرشيم", "price": 4370.00},
        {"code": "82740", "name": "استرونجر ويز يو", "company": "جولدن / أبرشيم", "price": 4040.00},
        {"code": "14724", "name": "اسكادا تاج", "company": "جولدن / أبرشيم", "price": 3640.00},
        {"code": "11150", "name": "اسكادا میامی بلوسوم", "company": "جولدن / أبرشيم", "price": 3520.00},
        {"code": "4724", "name": "اسكلبشر", "company": "جولدن / أبرشيم", "price": 2240.00},
        {"code": "11142", "name": "اسکندال", "company": "جولدن / أبرشيم", "price": 4210.00},
        {"code": "35884", "name": "اسکندال بای نایت", "company": "جولدن / أبرشيم", "price": 4430.00},
        {"code": "4022", "name": "اسکندال حریمی", "company": "جولدن / أبرشيم", "price": 4470.00},
        {"code": "2380", "name": "اسکندال رجالي", "company": "جولدن / أبرشيم", "price": 5110.00},
        {"code": "9516", "name": "اسكيب", "company": "جولدن / أبرشيم", "price": 3710.00},
        {"code": "10494", "name": "اسمیاکی", "company": "جولدن / أبرشيم", "price": 4410.00},
        {"code": "1870", "name": "اسمیاکی", "company": "جولدن / أبرشيم", "price": 4240.00},
        {"code": "8576", "name": "اغراء", "company": "جولدن / أبرشيم", "price": 3460.00},
        {"code": "8899", "name": "افريكان ليزر", "company": "جولدن / أبرشيم", "price": 6470.00},
        {"code": "6015G", "name": "افشان", "company": "جولدن / أبرشيم", "price": 4600.00},
        {"code": "146", "name": "اكس شيكولاته", "company": "جولدن / أبرشيم", "price": 3380.00},
        {"code": "1858", "name": "اكوا ديجيو", "company": "جولدن / أبرشيم", "price": 5040.00},
        {"code": "3102", "name": "اكوا ديجيو", "company": "جولدن / أبرشيم", "price": 2940.00},
        {"code": "3107", "name": "اكوا مان بلغاری", "company": "جولدن / أبرشيم", "price": 4170.00},
        {"code": "1948", "name": "الترامارين", "company": "جولدن / أبرشيم", "price": 4600.00},
        {"code": "66483", "name": "الكسندريا 3", "company": "جولدن / أبرشيم", "price": 8920.00},
        {"code": "7015G", "name": "الوسام", "company": "جولدن / أبرشيم", "price": 6720.00},
        {"code": "505", "name": "اليروسبورت", "company": "جولدن / أبرشيم", "price": 5140.00},
        {"code": "716", "name": "اليروسبورت", "company": "جولدن / أبرشيم", "price": 3310.00},
        {"code": "8585", "name": "انا الابيض", "company": "جولدن / أبرشيم", "price": 7340.00},
        {"code": "1813", "name": "انجل", "company": "جولدن / أبرشيم", "price": 3800.00},
        {"code": "5421", "name": "انجل نوفا", "company": "جولدن / أبرشيم", "price": 3890.00},
        {"code": "61320", "name": "انجل شیر کیلیان", "company": "جولدن / أبرشيم", "price": 4890.00},
        {"code": "61013", "name": "انفكتوس", "company": "جولدن / أبرشيم", "price": 3195.00},
        {"code": "3043", "name": "انفكتوس", "company": "جولدن / أبرشيم", "price": 5125.00},
        {"code": "74383", "name": "انفكتوس", "company": "جولدن / أبرشيم", "price": 4010.00},
        {"code": "62686", "name": "انفكتوس اونيكس كوليكتور", "company": "جولدن / أبرشيم", "price": 4240.00},
        {"code": "8599", "name": "انفكتوس بلاتنيوم", "company": "جولدن / أبرشيم", "price": 3820.00},
        {"code": "8605", "name": "انفكتوس بلاتنيوم مركز", "company": "جولدن / أبرشيم", "price": 4960.00},
        {"code": "51020", "name": "انفكتوس فكتوري", "company": "جولدن / أبرشيم", "price": 4450.00},
        {"code": "4340", "name": "انفكتوس فکتوری", "company": "جولدن / أبرشيم", "price": 4040.00},
        {"code": "57344", "name": "اوبسیسد حریمی", "company": "جولدن / أبرشيم", "price": 4660.00},
        {"code": "57965", "name": "اوبسیسد رجالی", "company": "جولدن / أبرشيم", "price": 3960.00},
        {"code": "3109", "name": "اورجانزا", "company": "جولدن / أبرشيم", "price": 4220.00},
        {"code": "3089", "name": "اوليمبيا", "company": "جولدن / أبرشيم", "price": 4650.00},
        {"code": "51281", "name": "اوليمبيا بلوسوم", "company": "جولدن / أبرشيم", "price": 3970.00},
        {"code": "11140", "name": "ايدول", "company": "جولدن / أبرشيم", "price": 4390.00},
        {"code": "3143", "name": "ايروس فيرزاتشي", "company": "جولدن / أبرشيم", "price": 4650.00},
        {"code": "3001", "name": "ايفوريا انتنس", "company": "جولدن / أبرشيم", "price": 4320.00},
        {"code": "3218", "name": "ایلی صعب", "company": "جولدن / أبرشيم", "price": 4090.00},
        {"code": "1541", "name": "أترنتي اخضر", "company": "جولدن / أبرشيم", "price": 4530.00},
        {"code": "55906", "name": "أكس ال", "company": "جولدن / أبرشيم", "price": 3540.00},
    ]
    
    with app.app_context():
        try:
            # إنشاء أو الحصول على الموردين الجدد
            companies = set([item["company"] for item in perfumes_data])
            suppliers = {}
            
            for company_name in companies:
                supplier = Supplier.query.filter_by(name=company_name).first()
                if not supplier:
                    supplier = Supplier(
                        name=company_name,
                        contact_person="غير محدد",
                        phone="غير محدد",
                        address="غير محدد"
                    )
                    db.session.add(supplier)
                    db.session.flush()
                suppliers[company_name] = supplier
            
            # إضافة العطور
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = suppliers[perfume_data["company"]]
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - {perfume_data['company']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=suppliers[perfume_data["company"]],
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - {perfume_data['company']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")
            print(f"📊 إجمالي العطور المعالجة: {len(perfumes_data)}")
            print(f"🏢 عدد الموردين الجدد: {len(companies)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد المجموعة الأخيرة من العطور...")
    print("=" * 70)
    
    success = import_final_batch()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("🌟 تم إكمال قاعدة البيانات الكاملة!")
        print("يمكنك الآن تشغيل النظام ومراجعة جميع العطور.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
