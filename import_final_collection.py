#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد المجموعة الأخيرة من العطور من موردين جدد
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_final_collection():
    """استيراد المجموعة الأخيرة من العطور"""
    
    # بيانات العطور الأخيرة
    perfumes_data = [
        # جولدن / أبرشيم - إضافات أخيرة
        {"code": "1633G", "name": "جيرل اوف ناو", "company": "جولدن / أبرشيم", "price": 4720.00},
        {"code": "5408G", "name": "سيدرات بويس مانسيرا", "company": "جولدن / أبرشيم", "price": 3900.00},
        {"code": "8685/125", "name": "كوبرا بلغاري", "company": "جولدن / أبرشيم", "price": 5500.00},
        {"code": "6505G", "name": "انفكتوس بارفيوم", "company": "جولدن / أبرشيم", "price": 5770.00},
        {"code": "63421G", "name": "ليرا زيرجوف", "company": "جولدن / أبرشيم", "price": 7360.00},
        {"code": "6078G", "name": "باي ذا فيبر لاس", "company": "جولدن / أبرشيم", "price": 3230.00},
        {"code": "5378", "name": "میاو کاتی بیری", "company": "جولدن / أبرشيم", "price": 4250.00},
        {"code": "4596", "name": "ایندی کاتی بیری", "company": "جولدن / أبرشيم", "price": 4500.00},
        {"code": "44/73801", "name": "كارمينا كريد", "company": "جولدن / أبرشيم", "price": 6500.00},
        {"code": "7117", "name": "بربری لندن", "company": "جولدن / أبرشيم", "price": 4950.00},
        {"code": "301G", "name": "کالان دی مارلی", "company": "جولدن / أبرشيم", "price": 6500.00},
        {"code": "3306", "name": "مسك بيستاشيو", "company": "جولدن / أبرشيم", "price": 5650.00},
        {"code": "3238", "name": "اربيانز تونكا مونتال", "company": "جولدن / أبرشيم", "price": 5500.00},
        {"code": "4035", "name": "کرید افنتوس", "company": "جولدن / أبرشيم", "price": 35650.00},
        {"code": "32/4278", "name": "الكسندريا 2 زيرجوف", "company": "جولدن / أبرشيم", "price": 9190.00},
        {"code": "6929", "name": "اكوا ديجيو برفومو", "company": "جولدن / أبرشيم", "price": 4000.00},
        {"code": "7192", "name": "وانتد ذا موست ازارو", "company": "جولدن / أبرشيم", "price": 3980.00},
        {"code": "4006", "name": "انفكتوس فكتوري الكسير", "company": "جولدن / أبرشيم", "price": 5940.00},
        {"code": "6546", "name": "لا مال لوفر جان بول", "company": "جولدن / أبرشيم", "price": 4960.00},
        {"code": "6624", "name": "فانيلا کاندی", "company": "جولدن / أبرشيم", "price": 6600.00},
        {"code": "6792", "name": "یارا کاندی", "company": "جولدن / أبرشيم", "price": 4500.00},
        {"code": "24047", "name": "اسد زنجبار", "company": "جولدن / أبرشيم", "price": 5700.00},
        {"code": "19245", "name": "يارا", "company": "جولدن / أبرشيم", "price": 4980.00},
        {"code": "10118", "name": "یارا کاندی", "company": "جولدن / أبرشيم", "price": 4980.00},
        {"code": "3091", "name": "الفارس العربي (العربية للعود)", "company": "جولدن / أبرشيم", "price": 5500.00},
        {"code": "3256", "name": "ازارو ونتد", "company": "جولدن / أبرشيم", "price": 4950.00},
        {"code": "3211", "name": "خمره", "company": "جولدن / أبرشيم", "price": 5150.00},
        {"code": "3258", "name": "اليروسبورت", "company": "جولدن / أبرشيم", "price": 4910.00},
        {"code": "6433", "name": "فلانتينو بورن روما انتنس", "company": "جولدن / أبرشيم", "price": 3350.00},
        {"code": "6536", "name": "اسكندال ابسوليو", "company": "جولدن / أبرشيم", "price": 4980.00},
        
        # اروما بلوم
        {"code": "37359", "name": "استيلا لوبيز", "company": "اروما بلوم", "price": 3950.00},
        {"code": "37579", "name": "انفكتوس", "company": "اروما بلوم", "price": 3860.00},
        {"code": "704403", "name": "برسیف حریمی", "company": "اروما بلوم", "price": 3850.00},
        {"code": "96338", "name": "بومب شيل", "company": "اروما بلوم", "price": 3800.00},
        {"code": "135101", "name": "تو باكلور", "company": "اروما بلوم", "price": 5160.00},
        {"code": "60011", "name": "ثری جی", "company": "اروما بلوم", "price": 3550.00},
        {"code": "60012", "name": "ثري جي بينك", "company": "اروما بلوم", "price": 3700.00},
        {"code": "47127", "name": "ثاجی جاجلر", "company": "اروما بلوم", "price": 4200.00},
        {"code": "67099", "name": "رويال", "company": "اروما بلوم", "price": 3300.00},
        {"code": "408466", "name": "رويال بلو", "company": "اروما بلوم", "price": 3950.00},
        {"code": "32635", "name": "ريل جولد", "company": "اروما بلوم", "price": 4100.00},
        {"code": "57441", "name": "سوفاج", "company": "اروما بلوم", "price": 3340.00},
        {"code": "46709", "name": "شالیز حریمی", "company": "اروما بلوم", "price": 3950.00},
        {"code": "49626", "name": "شيروتي", "company": "اروما بلوم", "price": 3950.00},
        {"code": "33334", "name": "عود اماراتی", "company": "اروما بلوم", "price": 4800.00},
        {"code": "75983", "name": "فوياج", "company": "اروما بلوم", "price": 4020.00},
        {"code": "1225A", "name": "الترامال", "company": "اروما بلوم", "price": 4400.00},
        {"code": "407690", "name": "کرید ایفنتوس", "company": "اروما بلوم", "price": 5450.00},
        {"code": "35091", "name": "کوتشی دریم", "company": "اروما بلوم", "price": 4720.00},
        {"code": "132187", "name": "لانتردي روج", "company": "اروما بلوم", "price": 4350.00},
        {"code": "361051", "name": "ليتل بلاك درس", "company": "اروما بلوم", "price": 3580.00},
        {"code": "127305", "name": "ماجيك", "company": "اروما بلوم", "price": 6950.00},
        {"code": "132181", "name": "هیروز فور ايفر", "company": "اروما بلوم", "price": 4100.00},
        {"code": "32857", "name": "نساء العالم", "company": "اروما بلوم", "price": 4020.00},
        {"code": "75121", "name": "سکریت شارم", "company": "اروما بلوم", "price": 3270.00},
        {"code": "876825", "name": "جوتشي بلوم", "company": "اروما بلوم", "price": 3350.00},
        {"code": "50521", "name": "جادور", "company": "اروما بلوم", "price": 3620.00},
        {"code": "6849", "name": "هيروز 212", "company": "اروما بلوم", "price": 3720.00},
        {"code": "5717", "name": "روز آن روزیس", "company": "اروما بلوم", "price": 3550.00},
        {"code": "4931", "name": "توباكو مندرين", "company": "اروما بلوم", "price": 3500.00},
        {"code": "40949", "name": "رن وایلد", "company": "اروما بلوم", "price": 3020.00},
        {"code": "40978", "name": "جوست بیوتی", "company": "اروما بلوم", "price": 3020.00},
        {"code": "7357", "name": "جود جيرل سوبريم", "company": "اروما بلوم", "price": 3460.00},
        {"code": "82329", "name": "بون بون", "company": "اروما بلوم", "price": 3700.00},
        {"code": "44215", "name": "مونت بلانك", "company": "اروما بلوم", "price": 4150.00},
        {"code": "46705", "name": "شالیز رجالي", "company": "اروما بلوم", "price": 3860.00},
        {"code": "81432", "name": "اكس شكولاته", "company": "اروما بلوم", "price": 3460.00},
        {"code": "2400231", "name": "اومبری لیزر", "company": "اروما بلوم", "price": 4670.00},
        {"code": "4877A", "name": "انفكتوس فكتوري", "company": "اروما بلوم", "price": 3420.00},
        {"code": "2190A", "name": "روبرتو كافالی", "company": "اروما بلوم", "price": 4100.00},
        {"code": "132186", "name": "اسكندال بور هوم", "company": "اروما بلوم", "price": 3180.00},
        {"code": "98390", "name": "بلاك اوبيوم", "company": "اروما بلوم", "price": 3700.00},
        {"code": "97629", "name": "عود الحرم اورينتال", "company": "اروما بلوم", "price": 5320.00},
        {"code": "700881", "name": "سلطان العطور", "company": "اروما بلوم", "price": 6370.00},
        {"code": "48068", "name": "صبايا", "company": "اروما بلوم", "price": 4100.00},
        {"code": "5757", "name": "عود بوكية", "company": "اروما بلوم", "price": 4720.00},
        {"code": "4486", "name": "بوص ذا سنت", "company": "اروما بلوم", "price": 3260.00},
        {"code": "4716A", "name": "استنشال سبورت", "company": "اروما بلوم", "price": 2900.00},
        {"code": "5752", "name": "سلفر سنت", "company": "اروما بلوم", "price": 4960.00},
        {"code": "5765", "name": "هوجو", "company": "اروما بلوم", "price": 3550.00},
        {"code": "5762", "name": "شيروتی", "company": "اروما بلوم", "price": 3660.00},
        {"code": "5438", "name": "اربا بورا", "company": "اروما بلوم", "price": 3180.00},
        {"code": "5723", "name": "باد بوی", "company": "اروما بلوم", "price": 3950.00},
        {"code": "5773A", "name": "بربری هیر", "company": "اروما بلوم", "price": 3850.00},
        {"code": "3923", "name": "بلاك ليكزس", "company": "اروما بلوم", "price": 3380.00},
        {"code": "3578", "name": "بلاك اوبيوم ستورم", "company": "اروما بلوم", "price": 3100.00},
        {"code": "4936", "name": "جيرل كان بي كريزي", "company": "اروما بلوم", "price": 3780.00},
        {"code": "3899", "name": "سكسي 212 رجالي", "company": "اروما بلوم", "price": 4070.00},
        {"code": "1233A", "name": "سو اسكندال", "company": "اروما بلوم", "price": 5850.00},
        {"code": "3618", "name": "فرى سكسي ناو", "company": "اروما بلوم", "price": 3580.00},
        {"code": "3442", "name": "رید بابی فلور", "company": "اروما بلوم", "price": 2780.00},
        {"code": "5727", "name": "مضاوي", "company": "اروما بلوم", "price": 5000.00},
        {"code": "5760", "name": "مید نایت", "company": "اروما بلوم", "price": 3550.00},
        {"code": "4923", "name": "نينا روز", "company": "اروما بلوم", "price": 4310.00},
        {"code": "1224A", "name": "وصال", "company": "اروما بلوم", "price": 5720.00},
        {"code": "3446", "name": "یس ای ام فابيلوس", "company": "اروما بلوم", "price": 3020.00},
        {"code": "5577", "name": "اسکندال بای نایت", "company": "اروما بلوم", "price": 3660.00},
        {"code": "5222", "name": "لافی ایبل", "company": "اروما بلوم", "price": 4460.00},
        {"code": "3180", "name": "لاكوست هو تايملس", "company": "اروما بلوم", "price": 3100.00},
        {"code": "5756A", "name": "دانهيل لندن", "company": "اروما بلوم", "price": 5080.00},
        {"code": "3646", "name": "كابوتين", "company": "اروما بلوم", "price": 2450.00},
        {"code": "5154", "name": "سی باشون", "company": "اروما بلوم", "price": 3180.00},
        {"code": "5747", "name": "سوفاج", "company": "اروما بلوم", "price": 5320.00},
        {"code": "1233B", "name": "سو اسکندال", "company": "اروما بلوم", "price": 5320.00},
        {"code": "1235", "name": "بوما سبورت", "company": "اروما بلوم", "price": 4880.00},
        {"code": "1238A", "name": "جس رجالي", "company": "اروما بلوم", "price": 4750.00},
        {"code": "5775", "name": "كاريزما", "company": "اروما بلوم", "price": 4100.00},
        {"code": "2400741", "name": "خمره (لطافه)", "company": "اروما بلوم", "price": 5050.00},
        
        # جيفودان
        {"code": "1254", "name": "تايجر بلغاری", "company": "جيفودان", "price": 8430.00},
        {"code": "2190G", "name": "اسکندال", "company": "جيفودان", "price": 3710.00},
        {"code": "80048", "name": "بيجاسوس دی مارلی", "company": "جيفودان", "price": 9420.00},
        {"code": "8940", "name": "كراش", "company": "جيفودان", "price": 8940.00},
        {"code": "1079", "name": "اكوا دي جيو بورفومو", "company": "جيفودان", "price": 4220.00},
        {"code": "1074", "name": "الترامال", "company": "جيفودان", "price": 3500.00},
        {"code": "2171", "name": "الين تيرى موجلر", "company": "جيفودان", "price": 3420.00},
        {"code": "1077", "name": "انفكتوس", "company": "جيفودان", "price": 3320.00},
        {"code": "2007", "name": "اوليمبيا", "company": "جيفودان", "price": 3830.00},
        {"code": "1166", "name": "باد دیزل", "company": "جيفودان", "price": 2950.00},
        {"code": "80093", "name": "بكرات روج 540", "company": "جيفودان", "price": 7650.00},
        {"code": "1172", "name": "بلاك افغانو", "company": "جيفودان", "price": 3320.00},
        {"code": "1238G", "name": "بلاك افغانو (نيش)", "company": "جيفودان", "price": 15940.00},
        {"code": "2200", "name": "بلاك اوبيوم", "company": "جيفودان", "price": 4080.00},
        {"code": "2025", "name": "بلاك اوركيد", "company": "جيفودان", "price": 5480.00},
        {"code": "2160", "name": "بون بون", "company": "جيفودان", "price": 3310.00},
        {"code": "2157", "name": "تريزوى لانوی", "company": "جيفودان", "price": 3980.00},
        {"code": "1241", "name": "توباكو فانيليا", "company": "جيفودان", "price": 10200.00},
        {"code": "2030", "name": "جادور", "company": "جيفودان", "price": 3640.00},
        {"code": "2016", "name": "جوتشي بامبو", "company": "جيفودان", "price": 4340.00},
        {"code": "1015", "name": "ديزاير بلاك", "company": "جيفودان", "price": 4410.00},
        {"code": "1173", "name": "ديلان بلو", "company": "جيفودان", "price": 3570.00},
        {"code": "2179", "name": "روز فانيليا", "company": "جيفودان", "price": 3200.00},
        {"code": "1242", "name": "ريد توباكو", "company": "جيفودان", "price": 9970.00},
        {"code": "2047", "name": "سکریت شارم", "company": "جيفودان", "price": 3590.00},
        {"code": "1063", "name": "سلفر ماونتن", "company": "جيفودان", "price": 4220.00},
        {"code": "1075", "name": "سوفاج", "company": "جيفودان", "price": 3640.00},
        {"code": "2188", "name": "سی ارمانی", "company": "جيفودان", "price": 3000.00},
        {"code": "2197", "name": "سي باشون", "company": "جيفودان", "price": 3920.00},
        {"code": "3111G", "name": "صقر الجزيرة", "company": "جيفودان", "price": 12500.00},
        {"code": "3149", "name": "عود ابيض", "company": "جيفودان", "price": 4730.00},
        {"code": "3146G", "name": "عود اماراتی", "company": "جيفودان", "price": 4050.00},
        {"code": "2096", "name": "عود بوكيه", "company": "جيفودان", "price": 4010.00},
        {"code": "1168", "name": "عود لينك (درعه)", "company": "جيفودان", "price": 3140.00},
        {"code": "2161", "name": "فانيليا مدغشقر", "company": "جيفودان", "price": 3710.00},
        {"code": "1160", "name": "فوياج", "company": "جيفودان", "price": 3230.00},
        {"code": "1214", "name": "في اي بي بلاك", "company": "جيفودان", "price": 3410.00},
        {"code": "2194", "name": "كوكو مودموذيل انتنس", "company": "جيفودان", "price": 4040.00},
        {"code": "1141", "name": "لیزر بلاند", "company": "جيفودان", "price": 4490.00},
        {"code": "1187", "name": "ليل سحرى", "company": "جيفودان", "price": 3980.00},
        {"code": "3109G", "name": "نیور سو عود", "company": "جيفودان", "price": 4080.00},
        {"code": "1002", "name": "هريرا 212", "company": "جيفودان", "price": 4300.00},
        {"code": "1179", "name": "هيفين", "company": "جيفودان", "price": 6010.00},
        {"code": "1039", "name": "وان میلیون", "company": "جيفودان", "price": 5520.00},
        
        # جوفيدان
        {"code": "3152G", "name": "عود وود", "company": "جوفيدان", "price": 8930.00},
        {"code": "1158", "name": "بلغاري مان ان بلاك", "company": "جوفيدان", "price": 4080.00},
        {"code": "1245", "name": "واى لايف سان لوران", "company": "جوفيدان", "price": 5630.00},
        {"code": "80118", "name": "ليمون اخضر", "company": "جوفيدان", "price": 1780.00},
        {"code": "80122", "name": "زارا", "company": "جوفيدان", "price": 2200.00},
        {"code": "80127", "name": "تفاح", "company": "جوفيدان", "price": 1580.00},
        {"code": "80129", "name": "نسيم البحر", "company": "جوفيدان", "price": 1850.00},
        {"code": "1240", "name": "بكرات روج مركز", "company": "جوفيدان", "price": 11240.00},
        {"code": "2216", "name": "ذا اونلي وان", "company": "جوفيدان", "price": 3790.00},
        {"code": "3153", "name": "عود اصفهان", "company": "جوفيدان", "price": 13600.00},
        {"code": "4017", "name": "انی نیشان", "company": "جوفيدان", "price": 12700.00},
        {"code": "80170", "name": "ديور هوم انتنس", "company": "جوفيدان", "price": 6800.00},
        {"code": "80184", "name": "وورم فانليا", "company": "جوفيدان", "price": 2850.00},
        {"code": "80160", "name": "هیدسون فالی (قصه)", "company": "جوفيدان", "price": 6600.00},
        {"code": "80163", "name": "سوفاج الكسير", "company": "جوفيدان", "price": 5800.00},
        
        # مان
        {"code": "324998", "name": "اسكلبشر", "company": "مان", "price": 2440.00},
        {"code": "2398", "name": "اسكلبشر مركز", "company": "مان", "price": 5230.00},
        {"code": "9901216", "name": "اسكيب حريمي", "company": "مان", "price": 4500.00},
        {"code": "5756M", "name": "اكوا ديجيو", "company": "مان", "price": 6780.00},
        {"code": "704015", "name": "اكوا ديجيو", "company": "مان", "price": 6720.00},
        {"code": "401461", "name": "اولد اسبايس", "company": "مان", "price": 2520.00},
        {"code": "520005", "name": "بلو جينز", "company": "مان", "price": 4060.00},
        {"code": "523168", "name": "بلاك اكس اس رجالي", "company": "مان", "price": 4230.00},
        {"code": "818845", "name": "وان مليون رجالي", "company": "مان", "price": 4290.00},
        {"code": "1415566", "name": "توباكو عود", "company": "مان", "price": 2650.00},
        {"code": "1777215", "name": "توباكو فانيليا", "company": "مان", "price": 3960.00},
        {"code": "9930057", "name": "جوتشی رش", "company": "مان", "price": 4630.00},
        {"code": "96443", "name": "دراكار", "company": "مان", "price": 5650.00},
        {"code": "9917154", "name": "رومبا", "company": "مان", "price": 4650.00},
        {"code": "105040", "name": "سيجار", "company": "مان", "price": 5470.00},
        {"code": "9717086", "name": "فهرنهایت", "company": "مان", "price": 6910.00},
        {"code": "121030", "name": "كاسيليا", "company": "مان", "price": 3990.00},
        {"code": "1732814", "name": "كلمات الذهب", "company": "مان", "price": 3790.00},
        {"code": "110161", "name": "لابيدوس", "company": "مان", "price": 3490.00},
        {"code": "7834", "name": "لاكوست بلاك", "company": "مان", "price": 2630.00},
        {"code": "1109703", "name": "لاكوست وايت", "company": "مان", "price": 2980.00},
        {"code": "93924", "name": "لانوى", "company": "مان", "price": 4380.00},
        {"code": "83817", "name": "ماجی نوار", "company": "مان", "price": 3400.00},
        {"code": "4636", "name": "مون سباركل", "company": "مان", "price": 4300.00},
        {"code": "939", "name": "ماربرت مان مرکز", "company": "مان", "price": 6060.00},
        {"code": "99082", "name": "وان مان شو", "company": "مان", "price": 4080.00},
        {"code": "75270", "name": "ورد بلدی", "company": "مان", "price": 4380.00},
        {"code": "107157", "name": "ويك اند", "company": "مان", "price": 3790.00},
        {"code": "627248", "name": "سكسي رجالي", "company": "مان", "price": 4440.00},
        {"code": "215543", "name": "هريرا 212", "company": "مان", "price": 5690.00},
        {"code": "14104/02", "name": "عود الحرمين 02", "company": "مان", "price": 4940.00},
        {"code": "14104/01", "name": "عود الحرمين 01", "company": "مان", "price": 2820.00},
        
        # فروما
        {"code": "1003F", "name": "وان مان شو", "company": "فروما", "price": 3240.00},
        {"code": "10271", "name": "شيروتي", "company": "فروما", "price": 3760.00},
        {"code": "2358", "name": "اربا بورا", "company": "فروما", "price": 6230.00},
        {"code": "9633", "name": "اسكلبشر 2000", "company": "فروما", "price": 3370.00},
        {"code": "9632", "name": "اسكلبشر 3000", "company": "فروما", "price": 4020.00},
        {"code": "2542", "name": "اسکندال", "company": "فروما", "price": 4615.00},
        {"code": "9644", "name": "اسكيب حريمي", "company": "فروما", "price": 2720.00},
        {"code": "20928", "name": "اكوا دي جيو", "company": "فروما", "price": 5345.00},
        {"code": "10039", "name": "انجل جولد", "company": "فروما", "price": 4345.00},
        {"code": "6124", "name": "اوليمبيا", "company": "فروما", "price": 4150.00},
        {"code": "1608", "name": "اون فایر", "company": "فروما", "price": 4020.00},
        {"code": "3990", "name": "باراواندا", "company": "فروما", "price": 9675.00},
        {"code": "1174", "name": "باشا كارتير", "company": "فروما", "price": 4020.00},
        {"code": "2647", "name": "بكرات روج", "company": "فروما", "price": 11820.00},
        {"code": "9868", "name": "بلاك ليكزس", "company": "فروما", "price": 4150.00},
        {"code": "1594", "name": "بلو شانيل", "company": "فروما", "price": 4670.00},
        {"code": "1746", "name": "بیور سیدکشن", "company": "فروما", "price": 3630.00},
        {"code": "10113", "name": "جوب نایت فلایت", "company": "فروما", "price": 4410.00},
        {"code": "8500", "name": "جود جيرل", "company": "فروما", "price": 4010.00},
        {"code": "9298", "name": "حريم السلطان", "company": "فروما", "price": 3890.00},
        {"code": "4684", "name": "دانهيل لندن", "company": "فروما", "price": 3630.00},
        {"code": "8903", "name": "سوفاج سوبر", "company": "فروما", "price": 5175.00},
        {"code": "1113", "name": "سی آرمانی", "company": "فروما", "price": 3890.00},
        {"code": "4678", "name": "شيخ الشيوخ", "company": "فروما", "price": 3760.00},
        {"code": "2052", "name": "عود بوكية", "company": "فروما", "price": 4020.00},
        {"code": "8895", "name": "فرى سكسي ناو", "company": "فروما", "price": 4020.00},
        {"code": "20457", "name": "كلمات", "company": "فروما", "price": 5265.00},
        {"code": "4524", "name": "کی دولسي جابانا", "company": "فروما", "price": 4020.00},
        {"code": "3992", "name": "لاف ذا هفنلی", "company": "فروما", "price": 4930.00},
        {"code": "9725", "name": "لافی ایبل", "company": "فروما", "price": 4150.00},
        {"code": "20771", "name": "لاكوست بلاك", "company": "فروما", "price": 3890.00},
        {"code": "2264", "name": "لامبر جيني", "company": "فروما", "price": 4605.00},
        {"code": "2785", "name": "ليبر انتنس", "company": "فروما", "price": 4420.00},
        {"code": "2340", "name": "مای وای", "company": "فروما", "price": 4150.00},
        {"code": "8492", "name": "هارمونی", "company": "فروما", "price": 8310.00},
        {"code": "1919", "name": "هوجو سوبر (3000)", "company": "فروما", "price": 5840.00},
        {"code": "5540", "name": "ويك اند", "company": "فروما", "price": 3760.00},
        {"code": "701", "name": "انترلود - امواج", "company": "فروما", "price": 6230.00},
        {"code": "1607", "name": "بربری رجالی", "company": "فروما", "price": 3495.00},
        {"code": "7651", "name": "ديكلاريش - كارتير", "company": "فروما", "price": 5515.00},
    ]
    
    with app.app_context():
        try:
            # إنشاء أو الحصول على الموردين الجدد
            companies = set([item["company"] for item in perfumes_data])
            suppliers = {}
            
            for company_name in companies:
                supplier = Supplier.query.filter_by(name=company_name).first()
                if not supplier:
                    supplier = Supplier(
                        name=company_name,
                        contact_person="غير محدد",
                        phone="غير محدد",
                        address="غير محدد"
                    )
                    db.session.add(supplier)
                    db.session.flush()
                suppliers[company_name] = supplier
            
            # إضافة العطور
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = suppliers[perfume_data["company"]]
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - {perfume_data['company']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=suppliers[perfume_data["company"]],
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - {perfume_data['company']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")
            print(f"📊 إجمالي العطور المعالجة: {len(perfumes_data)}")
            print(f"🏢 عدد الموردين الجدد: {len(companies)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد المجموعة الأخيرة من العطور...")
    print("=" * 70)
    
    success = import_final_collection()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("🌟 تم إكمال قاعدة البيانات الكاملة!")
        print("يمكنك الآن تشغيل النظام ومراجعة جميع العطور.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
