<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .navbar-brand { font-weight: bold; }
        .card { transition: transform 0.2s; }
        .card:hover { transform: translateY(-2px); }
        .stats-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .stats-card-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; }
        .stats-card-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; }
    </style>
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link active" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>
<div class="container">
  <!-- تم إلغاء تنبيهات المخزون المنخفض - النظام مفتوح -->
  {% if false %}
  <div class="alert alert-info text-center">
    <strong><i class="bi bi-info-circle"></i> النظام مفتوح</strong> - يمكن البيع بأي كمية بدون قيود المخزون
  </div>
  {% endif %}
  <div class="row justify-content-center">
    <div class="col-md-4 mb-4">
      <div class="card text-center shadow stats-card">
        <div class="card-body">
          <i class="bi bi-box display-4 mb-3"></i>
          <h5 class="card-title">عدد العطور</h5>
          <p class="display-4 fw-bold">{{ perfumes_count }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-4 mb-4">
      <div class="card text-center shadow stats-card-2">
        <div class="card-body">
          <i class="bi bi-receipt display-4 mb-3"></i>
          <h5 class="card-title">عدد الفواتير</h5>
          <p class="display-4 fw-bold">{{ invoices_count }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-4 mb-4">
      <div class="card text-center shadow stats-card-3">
        <div class="card-body">
          <i class="bi bi-currency-dollar display-4 mb-3"></i>
          <h5 class="card-title">إجمالي المبيعات</h5>
          <p class="display-4 fw-bold">{{ "{:,.0f}".format(total_sales) }}</p>
          <small>جنيه مصري</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="row mt-4">
    <div class="col-12">
      <h4 class="mb-3"><i class="bi bi-lightning"></i> إجراءات سريعة</h4>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/products/add" class="btn btn-success w-100 p-3">
        <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
        إضافة عطر جديد
      </a>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/invoices/add" class="btn btn-primary w-100 p-3">
        <i class="bi bi-receipt fs-4 d-block mb-2"></i>
        فاتورة جديدة
      </a>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/products" class="btn btn-info w-100 p-3">
        <i class="bi bi-search fs-4 d-block mb-2"></i>
        البحث في العطور
      </a>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/invoices" class="btn btn-warning w-100 p-3">
        <i class="bi bi-list-ul fs-4 d-block mb-2"></i>
        عرض الفواتير
      </a>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>