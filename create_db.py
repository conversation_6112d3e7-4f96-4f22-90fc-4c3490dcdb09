#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات بطريقة مبسطة
"""

import os
import sys
import sqlite3

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    
    # إنشاء مجلد data إذا لم يكن موجود
    if not os.path.exists('data'):
        os.makedirs('data')
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('data/store.db')
    cursor = conn.cursor()
    
    print("🗄️  إنشاء قاعدة البيانات...")
    
    # إنشاء جدول الأدوار
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS role (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
            description VARCHAR(200),
            permissions TEXT
        )
    ''')
    
    # إنشاء جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(150) UNIQUE NOT NULL,
            password_hash VARCHAR(150) NOT NULL,
            email VARCHAR(120),
            full_name VARCHAR(150),
            is_active BOOLEAN DEFAULT 1,
            last_login DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            role_id INTEGER DEFAULT 3,
            FOREIGN KEY (role_id) REFERENCES role (id)
        )
    ''')
    
    # إنشاء جدول الفئات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS category (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) UNIQUE NOT NULL,
            description TEXT
        )
    ''')
    
    # إنشاء جدول الموردين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS supplier (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(150) NOT NULL,
            contact_person VARCHAR(150),
            phone VARCHAR(20),
            email VARCHAR(120),
            address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول العملاء
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customer (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(150) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(120),
            address TEXT,
            birth_date DATE,
            gender VARCHAR(10),
            loyalty_points INTEGER DEFAULT 0,
            total_purchases FLOAT DEFAULT 0,
            last_purchase DATETIME,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول العطور
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS perfume (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(150) NOT NULL,
            company VARCHAR(150) NOT NULL,
            price FLOAT NOT NULL,
            cost_price FLOAT DEFAULT 0,
            quantity INTEGER NOT NULL,
            min_quantity INTEGER DEFAULT 5,
            max_quantity INTEGER DEFAULT 100,
            barcode VARCHAR(50) UNIQUE,
            sku VARCHAR(50) UNIQUE,
            size VARCHAR(20),
            expiry_date DATE,
            batch_number VARCHAR(50),
            location VARCHAR(100),
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            supplier_id INTEGER,
            category_id INTEGER,
            FOREIGN KEY (supplier_id) REFERENCES supplier (id),
            FOREIGN KEY (category_id) REFERENCES category (id)
        )
    ''')
    
    # إنشاء جدول الفواتير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoice (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date DATETIME NOT NULL,
            total FLOAT NOT NULL,
            subtotal FLOAT DEFAULT 0,
            discount FLOAT DEFAULT 0,
            tax FLOAT DEFAULT 0,
            payment_method VARCHAR(50) DEFAULT 'cash',
            status VARCHAR(20) DEFAULT 'completed',
            notes TEXT,
            customer_id INTEGER,
            created_by INTEGER,
            FOREIGN KEY (customer_id) REFERENCES customer (id),
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
    ''')
    
    # إنشاء جدول عناصر الفواتير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoice_item (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            perfume_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            price FLOAT NOT NULL,
            discount FLOAT DEFAULT 0,
            FOREIGN KEY (invoice_id) REFERENCES invoice (id),
            FOREIGN KEY (perfume_id) REFERENCES perfume (id)
        )
    ''')
    
    # إنشاء جدول حركات المخزون
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stock_movement (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            perfume_id INTEGER NOT NULL,
            movement_type VARCHAR(20) NOT NULL,
            quantity INTEGER NOT NULL,
            reason VARCHAR(100),
            reference_id INTEGER,
            reference_type VARCHAR(50),
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (perfume_id) REFERENCES perfume (id),
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
    ''')
    
    # إدراج البيانات الافتراضية
    print("📝 إدراج البيانات الافتراضية...")
    
    # إدراج الأدوار
    roles = [
        ('admin', 'مدير النظام - صلاحيات كاملة', '["manage_users", "manage_products", "manage_invoices", "manage_customers", "manage_suppliers", "view_reports", "manage_stock", "system_settings"]'),
        ('manager', 'مدير فرع - صلاحيات محدودة', '["manage_products", "manage_invoices", "manage_customers", "view_reports", "manage_stock"]'),
        ('employee', 'موظف - صلاحيات أساسية', '["manage_invoices", "view_products", "view_customers"]')
    ]
    
    cursor.executemany('INSERT OR IGNORE INTO role (name, description, permissions) VALUES (?, ?, ?)', roles)
    
    # إدراج المستخدم الافتراضي
    from werkzeug.security import generate_password_hash
    admin_password = generate_password_hash('ammar')
    cursor.execute('''
        INSERT OR IGNORE INTO user (username, password_hash, full_name, email, role_id) 
        VALUES (?, ?, ?, ?, ?)
    ''', ('ammar', admin_password, 'عمار - مدير النظام', '<EMAIL>', 1))
    
    # إدراج الفئات
    categories = [
        ('عطور رجالية', 'عطور مخصصة للرجال'),
        ('عطور نسائية', 'عطور مخصصة للنساء'),
        ('عطور مشتركة', 'عطور يمكن استخدامها للجنسين'),
        ('عطور أطفال', 'عطور مخصصة للأطفال')
    ]
    
    cursor.executemany('INSERT OR IGNORE INTO category (name, description) VALUES (?, ?)', categories)
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح!")
    print("\n📋 بيانات تسجيل الدخول:")
    print("👤 المستخدم: ammar")
    print("🔑 كلمة المرور: ammar")
    print("\n" + "="*50)

if __name__ == '__main__':
    create_database()
