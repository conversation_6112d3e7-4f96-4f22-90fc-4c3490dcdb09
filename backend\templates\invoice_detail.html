<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الفاتورة #{{ invoice.id }} - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
      @media print {
        .d-print-none { display: none !important; }
        body { background: white !important; }
        .card { border: none !important; box-shadow: none !important; }
      }
      .invoice-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
      .total-row { background-color: #f8f9fa; font-weight: bold; }
    </style>
    <script>
      function printInvoice() {
        window.print();
      }
    </script>
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow d-print-none">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-header invoice-header text-center">
          <h4 class="mb-0">
            <i class="bi bi-receipt"></i> فاتورة بيع رقم #{{ invoice.id }}
          </h4>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-6">
              <h6><i class="bi bi-calendar3"></i> معلومات الفاتورة</h6>
              <p class="mb-1"><strong>رقم الفاتورة:</strong> #{{ invoice.id }}</p>
              <p class="mb-1"><strong>التاريخ:</strong> {{ invoice.date.strftime('%Y-%m-%d') }}</p>
              <p class="mb-1"><strong>الوقت:</strong> {{ invoice.date.strftime('%H:%M') }}</p>
            </div>
            <div class="col-md-6 text-end">
              <h6><i class="bi bi-shop"></i> متجر العطور</h6>
              <p class="mb-1">نظام إدارة العطور</p>
              <p class="mb-1">فاتورة بيع</p>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table table-bordered table-hover bg-white">
              <thead class="table-light">
                <tr>
                  <th>اسم العطر</th>
                  <th>الشركة</th>
                  <th>السعر</th>
                  <th>الكمية</th>
                  <th>الإجمالي</th>
                </tr>
              </thead>
              <tbody>
                {% for item in items %}
                <tr>
                  <td><strong>{{ item.perfume.name }}</strong></td>
                  <td>{{ item.perfume.company }}</td>
                  <td><span class="text-success">{{ "{:,.2f}".format(item.price) }} ج.م</span></td>
                  <td><span class="badge bg-primary">{{ item.quantity }}</span></td>
                  <td><strong>{{ "{:,.2f}".format(item.price * item.quantity) }} ج.م</strong></td>
                </tr>
                {% endfor %}
                <tr class="total-row">
                  <td colspan="4" class="text-end fs-5">الإجمالي الكلي:</td>
                  <td class="fs-5 text-success"><strong>{{ "{:,.2f}".format(invoice.total) }} ج.م</strong></td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="d-print-none">
            <div class="row">
              <div class="col-md-4 mb-2">
                <button onclick="printInvoice()" class="btn btn-primary w-100">
                  <i class="bi bi-printer"></i> طباعة الفاتورة
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <a href="/invoices/{{ invoice.id }}/pdf" class="btn btn-success w-100" target="_blank">
                  <i class="bi bi-file-pdf"></i> تصدير PDF
                </a>
              </div>
              <div class="col-md-4 mb-2">
                <a href="/invoices" class="btn btn-outline-secondary w-100">
                  <i class="bi bi-arrow-left"></i> العودة للفواتير
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>