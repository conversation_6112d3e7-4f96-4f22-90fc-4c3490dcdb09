// إدارة الفواتير مع دعم البيع بالوزن
let items = [];
let allPerfumes = []; // قائمة جميع العطور للبحث
let selectedPerfume = null; // العطر المختار حالياً

// تحميل بيانات العطور من الخادم
function loadPerfumes() {
    // سيتم تحميل البيانات من المتغير العام perfumes_data
    if (typeof perfumes_data !== 'undefined') {
        allPerfumes = perfumes_data;
        console.log(`تم تحميل ${allPerfumes.length} عطر`);
    } else {
        console.error('لم يتم العثور على بيانات العطور');
    }
}

// البحث في العطور
function searchPerfumes() {
    const searchTerm = document.getElementById('perfume-search').value.toLowerCase().trim();
    const resultsDiv = document.getElementById('search-results');

    if (searchTerm.length < 1) {
        resultsDiv.style.display = 'none';
        return;
    }

    // البحث في العطور
    const filteredPerfumes = allPerfumes.filter(perfume => {
        const nameMatch = perfume.name.toLowerCase().includes(searchTerm);
        const companyMatch = perfume.company.toLowerCase().includes(searchTerm);
        const skuMatch = perfume.sku && perfume.sku.toLowerCase().includes(searchTerm);

        return nameMatch || companyMatch || skuMatch;
    });

    // عرض النتائج
    displaySearchResults(filteredPerfumes);
}

// تمييز النص المطابق
function highlightText(text, searchTerm) {
    if (!searchTerm) return text;
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="bg-warning">$1</mark>');
}

// عرض نتائج البحث
function displaySearchResults(perfumes) {
    const resultsDiv = document.getElementById('search-results');
    const searchTerm = document.getElementById('perfume-search').value.toLowerCase().trim();

    if (perfumes.length === 0) {
        resultsDiv.innerHTML = '<div class="p-3 text-muted"><i class="bi bi-search"></i> لا توجد نتائج مطابقة</div>';
        resultsDiv.style.display = 'block';
        return;
    }

    // إضافة عداد النتائج
    let html = `<div class="p-2 bg-light border-bottom">
        <small class="text-muted">
            <i class="bi bi-info-circle"></i>
            تم العثور على <strong>${perfumes.length}</strong> نتيجة
            ${perfumes.length > 10 ? ' (عرض أول 10 نتائج)' : ''}
        </small>
    </div>`;
    perfumes.slice(0, 10).forEach(perfume => { // عرض أول 10 نتائج فقط
        const priceDisplay = perfume.is_sold_by_weight ?
            `${perfume.price_per_kg.toLocaleString()} ج.م/كيلو` :
            `${perfume.price.toLocaleString()} ج.م/قطعة`;

        const quantityDisplay = perfume.is_sold_by_weight ?
            `متاح` :
            `متاح`;

        // تمييز النص المطابق
        const highlightedName = highlightText(perfume.name, searchTerm);
        const highlightedCompany = highlightText(perfume.company, searchTerm);
        const highlightedSku = perfume.sku ? highlightText(perfume.sku, searchTerm) : '';

        html += `
            <div class="search-result-item p-2 border-bottom"
                 style="cursor: pointer;"
                 onmousedown="selectPerfume(${perfume.id})"
                 onmouseover="this.style.backgroundColor='#f8f9fa'"
                 onmouseout="this.style.backgroundColor='white'">
                <div class="fw-bold">${highlightedName}</div>
                <div class="text-muted small">
                    ${highlightedCompany} | ${priceDisplay} | ${quantityDisplay}
                    ${perfume.sku ? ` | كود: ${highlightedSku}` : ''}
                </div>
            </div>
        `;
    });

    if (perfumes.length > 10) {
        html += `<div class="p-2 text-center text-muted small">وأكثر من ${perfumes.length - 10} نتيجة أخرى...</div>`;
    }

    resultsDiv.innerHTML = html;
    resultsDiv.style.display = 'block';
}

// اختيار عطر من نتائج البحث
function selectPerfume(perfumeId) {
    const perfume = allPerfumes.find(p => p.id === perfumeId);
    if (!perfume) return;

    selectedPerfume = perfume;
    document.getElementById('perfume-search').value = `${perfume.name} - ${perfume.company}`;
    document.getElementById('selected-perfume-id').value = perfume.id;
    document.getElementById('search-results').style.display = 'none';

    updatePerfumeInfo();
}

// إظهار نتائج البحث
function showSearchResults() {
    const searchTerm = document.getElementById('perfume-search').value;
    if (searchTerm.length >= 1) {
        searchPerfumes();
    }
}

// إخفاء نتائج البحث
function hideSearchResults() {
    setTimeout(() => {
        document.getElementById('search-results').style.display = 'none';
    }, 200); // تأخير قصير للسماح بالنقر على النتائج
}

function updatePerfumeInfo() {
    const pieceSection = document.getElementById('piece_quantity_section');
    const weightSection = document.getElementById('weight_quantity_section');

    if (selectedPerfume) {
        const isSoldByWeight = selectedPerfume.is_sold_by_weight;

        if (isSoldByWeight) {
            pieceSection.style.display = 'none';
            weightSection.style.display = 'block';
            calculateWeightPrice();
        } else {
            pieceSection.style.display = 'block';
            weightSection.style.display = 'none';
            // إزالة قيد الحد الأقصى للكمية
            // const quantityInput = document.getElementById('quantity_piece');
            // quantityInput.max = selectedPerfume.quantity;
        }
    } else {
        pieceSection.style.display = 'none';
        weightSection.style.display = 'none';
    }
}

function calculateWeightPrice() {
    const weightInput = document.getElementById('quantity_weight');
    const priceDisplay = document.getElementById('weight_price_display');

    if (selectedPerfume && weightInput.value) {
        const pricePerKg = parseFloat(selectedPerfume.price_per_kg);
        const weight = parseFloat(weightInput.value);
        const totalPrice = (pricePerKg / 1000) * weight;

        priceDisplay.textContent = `السعر: ${totalPrice.toFixed(2)} ج.م`;
    }
}

function addItem() {
    if (!selectedPerfume) {
        alert('يرجى اختيار عطر أولاً');
        return;
    }

    const isSoldByWeight = selectedPerfume.is_sold_by_weight;
    let quantity, weight = 0, unitPrice, totalPrice;

    if (isSoldByWeight) {
        weight = parseFloat(document.getElementById('quantity_weight').value);
        if (!weight || weight <= 0) {
            alert('يرجى إدخال وزن صحيح');
            return;
        }

        // إزالة قيد الكمية المتاحة - النظام مفتوح
        // const availableWeight = parseInt(selectedPerfume.quantity);
        // if (weight > availableWeight) {
        //     alert(`الوزن المطلوب أكبر من المتوفر (${availableWeight} جرام)`);
        //     return;
        // }

        const pricePerKg = parseFloat(selectedPerfume.price_per_kg);
        unitPrice = pricePerKg / 1000; // سعر الجرام
        totalPrice = unitPrice * weight;
        quantity = weight; // نحفظ الوزن كـ quantity
    } else {
        quantity = parseInt(document.getElementById('quantity_piece').value);
        if (!quantity || quantity <= 0) {
            alert('يرجى إدخال كمية صحيحة');
            return;
        }

        // إزالة قيد الكمية المتاحة - النظام مفتوح
        // const availableQuantity = parseInt(selectedPerfume.quantity);
        // if (quantity > availableQuantity) {
        //     alert(`الكمية المطلوبة أكبر من المتوفر (${availableQuantity} قطعة)`);
        //     return;
        // }

        unitPrice = parseFloat(selectedPerfume.price);
        totalPrice = unitPrice * quantity;
    }

    const perfume = {
        id: selectedPerfume.id,
        name: selectedPerfume.name,
        company: selectedPerfume.company,
        price: unitPrice,
        quantity: parseInt(selectedPerfume.quantity),
        isSoldByWeight: isSoldByWeight,
        pricePerKg: parseFloat(selectedPerfume.price_per_kg) || 0
    };

    // Check if item already exists
    const existingIndex = items.findIndex(item => item.perfume.id == perfume.id);
    if (existingIndex >= 0) {
        if (isSoldByWeight) {
            items[existingIndex].weight += weight;
            items[existingIndex].quantity += weight;
            items[existingIndex].totalPrice += totalPrice;
        } else {
            items[existingIndex].quantity += quantity;
            items[existingIndex].totalPrice += totalPrice;
        }
    } else {
        items.push({
            perfume,
            quantity,
            weight: weight,
            unitPrice,
            totalPrice,
            isSoldByWeight
        });
    }

    // Reset form
    selectedPerfume = null;
    document.getElementById('perfume-search').value = '';
    document.getElementById('selected-perfume-id').value = '';
    document.getElementById('quantity_piece').value = 1;
    document.getElementById('quantity_weight').value = 100;
    document.getElementById('weight_price_display').textContent = '';
    updatePerfumeInfo();
    renderItems();
}

function removeItem(idx) {
    items.splice(idx, 1);
    renderItems();
}

function renderItems() {
    const tbody = document.getElementById('items-body');
    tbody.innerHTML = '';
    let total = 0;
    
    items.forEach((item, idx) => {
        total += item.totalPrice;
        
        const quantityDisplay = item.isSoldByWeight ? 
            `${item.weight.toFixed(0)} جرام` : 
            `${item.quantity} قطعة`;
            
        const priceDisplay = item.isSoldByWeight ?
            `${item.unitPrice.toFixed(3)} ج.م/جرام` :
            `${item.unitPrice.toFixed(2)} ج.م/قطعة`;
        
        tbody.innerHTML += `
            <tr>
                <td><strong>${item.perfume.name}</strong></td>
                <td>${item.perfume.company}</td>
                <td><span class="text-success fw-bold">${priceDisplay}</span></td>
                <td>
                    <span class="badge bg-primary">${quantityDisplay}</span>
                </td>
                <td><span class="fw-bold">${item.totalPrice.toFixed(2)} ج.م</span></td>
                <td>
                    <button type='button' class='btn btn-danger btn-sm' onclick='removeItem(${idx})' title="حذف">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>`;
    });
    
    document.getElementById('total').innerText = total.toFixed(2) + ' ج.م';
    
    // تحضير البيانات للإرسال
    const itemsData = items.map(item => ({
        id: item.perfume.id,
        quantity: item.isSoldByWeight ? item.weight : item.quantity,
        weight: item.weight || 0,
        is_weight_based: item.isSoldByWeight,
        unit_price: item.unitPrice,
        total_price: item.totalPrice
    }));
    
    document.getElementById('items-data').value = JSON.stringify(itemsData);
    
    // Update submit button state
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.disabled = items.length === 0;
    }
}

function updateQuantity(idx, newQuantity) {
    if (newQuantity > 0) {
        const item = items[idx];
        if (item.isSoldByWeight) {
            if (newQuantity <= item.perfume.quantity) {
                item.weight = newQuantity;
                item.quantity = newQuantity;
                item.totalPrice = item.unitPrice * newQuantity;
                renderItems();
            }
        } else {
            if (newQuantity <= item.perfume.quantity) {
                item.quantity = parseInt(newQuantity);
                item.totalPrice = item.unitPrice * item.quantity;
                renderItems();
            }
        }
    }
}

// تشغيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadPerfumes();
    renderItems();
    updatePerfumeInfo();

    // إضافة مستمع للضغط على Enter في حقل البحث
    document.getElementById('perfume-search').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const results = document.querySelectorAll('.search-result-item');
            if (results.length > 0) {
                // اختيار أول نتيجة عند الضغط على Enter
                const firstResult = results[0];
                const perfumeId = firstResult.getAttribute('onmousedown').match(/\d+/)[0];
                selectPerfume(parseInt(perfumeId));
            }
        }
    });
});
