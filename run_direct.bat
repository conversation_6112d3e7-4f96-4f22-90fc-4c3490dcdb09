@echo off
title Perfume Store System

echo Starting Perfume Store Management System...
echo.

REM Install dependencies
pip install Flask Flask-Login Flask-SQLAlchemy pandas reportlab xlsxwriter

REM Create database if needed
if not exist "data\store.db" (
    echo Creating database...
    python create_db.py
)

echo.
echo System starting on: http://127.0.0.1:5000
echo Default login: ammar / ammar
echo Press Ctrl+C to stop
echo.

python run.py

pause
