<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء فاتورة بيع - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .perfume-card { cursor: pointer; transition: all 0.3s; }
        .perfume-card:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .perfume-card.selected { border-color: #0d6efd; background-color: #e7f3ff; }
        .quantity-input { max-width: 80px; }
        .total-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }

        /* تصميم البحث */
        #perfume-search {
            border: 2px solid #e9ecef;
            transition: border-color 0.3s;
        }
        #perfume-search:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .search-result-item {
            transition: background-color 0.2s;
            border-left: 3px solid transparent;
        }
        .search-result-item:hover {
            background-color: #f8f9fa !important;
            border-left-color: #0d6efd;
        }
        #search-results {
            border-top: none;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
    </style>

</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-body">
          <h4 class="mb-3 text-center">
            <i class="bi bi-receipt"></i> إنشاء فاتورة بيع جديدة
          </h4>

          <!-- عداد العطور المتاحة -->
          <div class="alert alert-info text-center mb-4">
            <i class="bi bi-info-circle me-2"></i>
            <span>العطور المتاحة: <strong>{{ total_perfumes_count }}</strong> عطر</span>
          </div>

          <!-- Flash Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }} alert-dismissible fade show">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
              {% endfor %}
            {% endif %}
          {% endwith %}

          <form method="post">
            <div class="card mb-4">
              <div class="card-header bg-light">
                <h6 class="mb-0"><i class="bi bi-plus-circle"></i> إضافة منتج للفاتورة</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">
                      <i class="bi bi-search"></i> ابحث عن العطر
                    </label>
                    <div class="position-relative">
                      <input type="text" id="perfume-search" class="form-control"
                             placeholder="🔍 ابحث عن العطر (اسم، شركة، أو كود)..."
                             autocomplete="off"
                             onkeyup="searchPerfumes()"
                             onfocus="showSearchResults()"
                             onblur="hideSearchResults()">
                      <div id="search-results" class="position-absolute w-100 bg-white border rounded shadow-sm"
                           style="display: none; max-height: 300px; overflow-y: auto; z-index: 1000; top: 100%;">
                        <!-- نتائج البحث ستظهر هنا -->
                      </div>
                    </div>
                    <input type="hidden" id="selected-perfume-id" value="">
                  </div>

                  <!-- حقول الكمية للقطعة -->
                  <div class="col-md-3 mb-3" id="piece_quantity_section" style="display: none;">
                    <label class="form-label">الكمية (قطعة)</label>
                    <input type="number" id="quantity_piece" class="form-control" min="1" value="1">
                  </div>

                  <!-- حقول الوزن -->
                  <div class="col-md-3 mb-3" id="weight_quantity_section" style="display: none;">
                    <label class="form-label">الوزن (جرام)</label>
                    <input type="number" id="quantity_weight" class="form-control" min="1" value="100" onchange="calculateWeightPrice()">
                    <small class="text-muted" id="weight_price_display"></small>
                  </div>
                  <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="button" class="btn btn-success w-100" onclick="addItem()">
                      <i class="bi bi-plus-circle"></i> إضافة
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="table-responsive mb-3">
              <table class="table table-bordered table-hover bg-white">
                <thead class="table-light">
                  <tr>
                    <th>اسم العطر</th>
                    <th>الشركة</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>الإجمالي</th>
                    <th>إجراء</th>
                  </tr>
                </thead>
                <tbody id="items-body"></tbody>
                <tfoot class="total-section">
                  <tr>
                    <th colspan="4" class="text-end fs-5">الإجمالي الكلي:</th>
                    <th colspan="2" class="fs-4" id="total">0 ج.م</th>
                  </tr>
                </tfoot>
              </table>
            </div>

            <input type="hidden" name="items" id="items-data">

            <div class="d-grid gap-2">
              <button type="submit" id="submit-btn" class="btn btn-primary btn-lg" disabled>
                <i class="bi bi-save"></i> حفظ الفاتورة
              </button>
              <a href="/invoices" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> العودة لسجل الفواتير
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
  // Update quantity max when perfume is selected
  document.getElementById('perfume').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const quantityInput = document.getElementById('quantity');

    if (selectedOption.value) {
      const maxQuantity = selectedOption.getAttribute('data-quantity');
      quantityInput.max = maxQuantity;
      quantityInput.value = Math.min(quantityInput.value, maxQuantity);
    } else {
      quantityInput.max = 1;
      quantityInput.value = 1;
    }
  });
</script>

<!-- تمرير بيانات العطور إلى JavaScript -->
<script>
  const perfumes_data = {{ perfumes_data|tojson }};
</script>

<script src="{{ url_for('static', filename='js/invoice.js') }}"></script>
</body>
</html>