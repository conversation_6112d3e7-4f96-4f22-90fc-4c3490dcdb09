<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>

<div class="container">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card shadow form-card">
        <div class="card-header bg-primary text-white text-center">
          <h4 class="mb-0">
            <i class="bi bi-{% if supplier %}pencil{% else %}plus-circle{% endif %}"></i> 
            {{ title }}
          </h4>
        </div>
        <div class="card-body">
          <!-- Flash Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'danger' else 'success' }} alert-dismissible fade show">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
              {% endfor %}
            {% endif %}
          {% endwith %}
          
          <form method="post" novalidate>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-building"></i> اسم المورد <span class="required">*</span>
                </label>
                <input type="text" name="name" class="form-control" required 
                       value="{{ supplier.name if supplier else '' }}"
                       placeholder="أدخل اسم المورد">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-person"></i> الشخص المسؤول
                </label>
                <input type="text" name="contact_person" class="form-control" 
                       value="{{ supplier.contact_person if supplier else '' }}"
                       placeholder="اسم الشخص المسؤول">
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-telephone"></i> رقم الهاتف
                </label>
                <input type="tel" name="phone" class="form-control" 
                       value="{{ supplier.phone if supplier else '' }}"
                       placeholder="01xxxxxxxxx">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="bi bi-envelope"></i> البريد الإلكتروني
                </label>
                <input type="email" name="email" class="form-control" 
                       value="{{ supplier.email if supplier else '' }}"
                       placeholder="<EMAIL>">
              </div>
            </div>
            
            <div class="mb-3">
              <label class="form-label">
                <i class="bi bi-geo-alt"></i> العنوان
              </label>
              <textarea name="address" class="form-control" rows="3" 
                        placeholder="أدخل العنوان الكامل">{{ supplier.address if supplier else '' }}</textarea>
            </div>
            
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-success btn-lg">
                <i class="bi bi-save"></i> حفظ البيانات
              </button>
              <a href="/suppliers" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> العودة لقائمة الموردين
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
