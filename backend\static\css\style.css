/* نظام إدارة العطور - ملف التصميم الرئيسي */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* تحسينات عامة */
body {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* تحسين النافبار */
.navbar {
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

/* تحسين الكروت */
.card {
    border: none;
    border-radius: 1rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* كروت الإحصائيات */
.stats-card {
    background: var(--gradient-1);
    color: white;
    border: none;
}

.stats-card-2 {
    background: var(--gradient-2);
    color: white;
    border: none;
}

.stats-card-3 {
    background: var(--gradient-3);
    color: white;
    border: none;
}

.stats-card .display-4,
.stats-card-2 .display-4,
.stats-card-3 .display-4 {
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* تحسين الأزرار */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: var(--gradient-1);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.btn-info {
    background: var(--gradient-3);
}

/* تحسين الجداول */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: var(--gradient-1);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

.table-responsive {
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
}

/* تحسين النماذج */
.form-control,
.form-select {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 0.75rem;
    font-weight: 500;
    box-shadow: var(--shadow);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
}

/* تحسين المخزون المنخفض */
.low-stock {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

/* تحسين الفواتير */
.invoice-header {
    background: var(--gradient-1);
    color: white;
}

.total-section {
    background: var(--gradient-1);
    color: white;
}

.invoice-row {
    transition: all 0.2s ease;
}

.invoice-row:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateX(5px);
}

.invoice-total {
    font-weight: 700;
    color: var(--success-color);
    font-size: 1.1rem;
}

/* تحسين الأيقونات */
.bi {
    margin-left: 0.5rem;
}

/* تحسين الإجراءات السريعة */
.quick-action-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: var(--dark-color);
    transition: all 0.3s ease;
    text-decoration: none;
}

.quick-action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* تحسين الطباعة */
@media print {
    .d-print-none {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .navbar {
        display: none !important;
    }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        margin-bottom: 0.5rem;
    }
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0b5ed7;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين النصوص */
.text-gradient {
    background: var(--gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تحسين الحاويات */
.container {
    max-width: 1200px;
}

/* تحسين الفواصل */
hr {
    border: none;
    height: 2px;
    background: var(--gradient-1);
    border-radius: 1px;
    margin: 2rem 0;
}
