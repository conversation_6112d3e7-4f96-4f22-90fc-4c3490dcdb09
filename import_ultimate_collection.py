#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد المجموعة النهائية من العطور - أكبر مجموعة
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_ultimate_collection():
    """استيراد المجموعة النهائية من العطور"""

    # بيانات العطور النهائية - الجزء الأول
    perfumes_data_part1 = [
        # فروما - إضافات فاخرة
        {"code": "151", "name": "هیرود دیی مارلی", "company": "فروما", "price": 4970.00},
        {"code": "222", "name": "ميجامار", "company": "فروما", "price": 9990.00},
        {"code": "1917", "name": "رغبه", "company": "فروما", "price": 4370.00},
        {"code": "936", "name": "لايتون دي مارلی", "company": "فروما", "price": 5970.00},
        {"code": "592", "name": "بستاشيو جيلاتو", "company": "فروما", "price": 4370.00},
        {"code": "182", "name": "حیاتی فلور انس", "company": "فروما", "price": 9970.00},
        {"code": "164", "name": "مسك الخليج (اجمل)", "company": "فروما", "price": 5960.00},
        {"code": "508", "name": "مسك الحرمين", "company": "فروما", "price": 4370.00},
        {"code": "853", "name": "لامال برايد (2024)", "company": "فروما", "price": 4970.00},
        {"code": "786", "name": "لو بو باراديس جارد", "company": "فروما", "price": 4970.00},
        {"code": "886", "name": "نبراس (لطافه)", "company": "فروما", "price": 3470.00},
        {"code": "3300", "name": "استرونجر ويز يو ارم", "company": "فروما", "price": 4970.00},
        {"code": "3813", "name": "لامال الكسير", "company": "فروما", "price": 5290.00},
        {"code": "605F", "name": "كلاود بينك", "company": "فروما", "price": 6970.00},
        {"code": "6604", "name": "جوديس", "company": "فروما", "price": 5930.00},
        {"code": "605", "name": "بوص ذا سنت الكسير", "company": "فروما", "price": 5970.00},
        {"code": "6610", "name": "لامال الكسير", "company": "فروما", "price": 4970.00},
        {"code": "6621", "name": "باسيفيك شيل", "company": "فروما", "price": 9770.00},
        {"code": "117", "name": "خمره قهوه (لطافه)", "company": "فروما", "price": 6970.00},
        {"code": "105", "name": "استرونجر ويز يو", "company": "فروما", "price": 5970.00},
        {"code": "238", "name": "لامال لافر", "company": "فروما", "price": 4980.00},
        {"code": "807", "name": "بی ام 9 افنان)", "company": "فروما", "price": 6970.00},
        {"code": "3817", "name": "لامال الكسير", "company": "فروما", "price": 5970.00},
        {"code": "523", "name": "اومو انتس", "company": "فروما", "price": 8900.00},
        {"code": "742", "name": "نوار اکستروم", "company": "فروما", "price": 11900.00},
        {"code": "618", "name": "روز بريك", "company": "فروما", "price": 15600.00},
        {"code": "922", "name": "فاكينج فابيلوس", "company": "فروما", "price": 9900.00},
        {"code": "611", "name": "عود وود", "company": "فروما", "price": 12900.00},
        {"code": "9", "name": "بلاك لاكر", "company": "فروما", "price": 6920.00},
        {"code": "724", "name": "هاسیفات نیشان", "company": "فروما", "price": 15900.00},
        {"code": "777", "name": "فلاورز اند فلیمز", "company": "فروما", "price": 6970.00},
        {"code": "20", "name": "مارشينو سوسبيروا", "company": "فروما", "price": 9875.00},
        {"code": "287", "name": "الثائر دی مارلی (مرکز)", "company": "فروما", "price": 9900.00},
        {"code": "874", "name": "فلور ناركوتيك (مركز)", "company": "فروما", "price": 15970.00},
        {"code": "997", "name": "سولیل نیجی (مرکز)", "company": "فروما", "price": 17800.00},
        {"code": "570", "name": "مايسون فرانسيس", "company": "فروما", "price": 8900.00},
        {"code": "700", "name": "اكوا ميديا", "company": "فروما", "price": 12900.00},
        {"code": "519", "name": "جنتل فلويديتي سلفر", "company": "فروما", "price": 14900.00},
        {"code": "925", "name": "ایما جنيشن لويز فيتون)", "company": "فروما", "price": 29700.00},
        {"code": "771", "name": "لو بو بارادايس جارد", "company": "فروما", "price": 9870.00},

        # روائع
        {"code": "19037", "name": "مسك العروسه", "company": "روائع", "price": 2970.00},
        {"code": "19045", "name": "هوس", "company": "روائع", "price": 3370.00},
        {"code": "19006", "name": "عود موود", "company": "روائع", "price": 3490.00},
        {"code": "19018", "name": "دخون العود", "company": "روائع", "price": 2970.00},
        {"code": "19013", "name": "شغف", "company": "روائع", "price": 2970.00},
        {"code": "19015", "name": "قائد الفرسان", "company": "روائع", "price": 3970.00},
        {"code": "19039", "name": "القناص", "company": "روائع", "price": 2970.00},
        {"code": "19044", "name": "اكوا بلغاری", "company": "روائع", "price": 5970.00},
        {"code": "19027", "name": "سبرتا روز", "company": "روائع", "price": 3370.00},
        {"code": "19020", "name": "مليونير", "company": "روائع", "price": 3490.00},
        {"code": "19043", "name": "بلاك افغانوا", "company": "روائع", "price": 14970.00},

        # يورو
        {"code": "1460", "name": "کاتی بیری", "company": "يورو", "price": 3100.00},
        {"code": "1819", "name": "جیمی شو", "company": "يورو", "price": 3500.00},
        {"code": "1538", "name": "سوفاج", "company": "يورو", "price": 4950.00},
        {"code": "1748", "name": "سی باشون", "company": "يورو", "price": 5450.00},
        {"code": "1304", "name": "كلمات", "company": "يورو", "price": 5180.00},

        # لارين
        {"code": "7768", "name": "روسيندو ماتيو", "company": "لارين", "price": 11500.00},
        {"code": "7266", "name": "روج سرايا", "company": "لارين", "price": 4900.00},

        # Agl
        {"code": "12115", "name": "انکاندسنس", "company": "Agl", "price": 3620.00},
        {"code": "12146", "name": "برسیف حریمی", "company": "Agl", "price": 2855.00},
        {"code": "17301", "name": "برسیف رجالي", "company": "Agl", "price": 2625.00},
        {"code": "17488", "name": "بلاك اوبيوم", "company": "Agl", "price": 4000.00},
        {"code": "12138", "name": "تندرا", "company": "Agl", "price": 2785.00},
        {"code": "12344", "name": "تومورو", "company": "Agl", "price": 3075.00},
        {"code": "12156", "name": "جوردانی جولد", "company": "Agl", "price": 2620.00},
        {"code": "12287", "name": "جوردانی وایت", "company": "Agl", "price": 3105.00},
        {"code": "12197", "name": "جولدن تاتش", "company": "Agl", "price": 2510.00},
        {"code": "12229", "name": "رير بربيز 2000", "company": "Agl", "price": 2165.00},
        {"code": "12227", "name": "رير جولد", "company": "Agl", "price": 3075.00},
        {"code": "17328", "name": "فول سبيد", "company": "Agl", "price": 3075.00},
        {"code": "12187", "name": "لتل بلاك", "company": "Agl", "price": 3240.00},
        {"code": "17371", "name": "میز مرايز 2000", "company": "Agl", "price": 3240.00},
        {"code": "19127", "name": "ورد بلدی", "company": "Agl", "price": 1715.00},
    ]

    with app.app_context():
        try:
            # إنشاء أو الحصول على الموردين الجدد
            companies = set([item["company"] for item in perfumes_data_part1])
            suppliers = {}

            for company_name in companies:
                supplier = Supplier.query.filter_by(name=company_name).first()
                if not supplier:
                    supplier = Supplier(
                        name=company_name,
                        contact_person="غير محدد",
                        phone="غير محدد",
                        address="غير محدد"
                    )
                    db.session.add(supplier)
                    db.session.flush()
                suppliers[company_name] = supplier

            # إضافة العطور - الجزء الأول
            added_count = 0
            updated_count = 0

            for perfume_data in perfumes_data_part1:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()

                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = suppliers[perfume_data["company"]]
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - {perfume_data['company']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=suppliers[perfume_data["company"]],
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - {perfume_data['company']}")

            print(f"\n✅ تم الانتهاء من الجزء الأول!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")

            return added_count, updated_count, suppliers

        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ في الجزء الأول: {str(e)}")
            return 0, 0, {}

def import_part2(suppliers):
    """استيراد الجزء الثاني من العطور"""

    # بيانات العطور النهائية - الجزء الثاني (ese)
    perfumes_data_part2 = [
        # ese - مجموعة ضخمة
        {"code": "70002", "name": "اترنتی", "company": "ese", "price": 3490.00},
        {"code": "97516", "name": "اديداس بلاك", "company": "ese", "price": 2799.00},
        {"code": "24404", "name": "اربا بورا - ساسبيرو", "company": "ese", "price": 5175.00},
        {"code": "70140", "name": "اربا بورا - ساسبيرو", "company": "ese", "price": 3573.00},
        {"code": "10122", "name": "ارمانی کود ان لیجند", "company": "ese", "price": 3790.00},
        {"code": "97515", "name": "ازارو كروم ليجند", "company": "ese", "price": 2907.00},
        {"code": "1001/B", "name": "استرونجر وذ يو", "company": "ese", "price": 2128.00},
        {"code": "9865", "name": "استرونجر وذ يو", "company": "ese", "price": 2691.00},
        {"code": "97562", "name": "استرونجر وذ يو (نخب اول)", "company": "ese", "price": 3231.00},
        {"code": "6666", "name": "اسكادا كولكشن", "company": "ese", "price": 2691.00},
        {"code": "4121", "name": "اسكلبشر", "company": "ese", "price": 2241.00},
        {"code": "9757", "name": "اسكلبشر (نخب اول)", "company": "ese", "price": 2961.00},
        {"code": "70105", "name": "اسمیاکی", "company": "ese", "price": 2470.00},
        {"code": "70102", "name": "اكسنتو (نخب اول)", "company": "ese", "price": 6970.00},
        {"code": "62508", "name": "اكوا دي جيو", "company": "ese", "price": 2691.00},
        {"code": "4001", "name": "اكوا دي جيو 1000", "company": "ese", "price": 3141.00},
        {"code": "975001", "name": "اکوا دی جیو نخب اول)", "company": "ese", "price": 3861.00},
        {"code": "109", "name": "الترامارين", "company": "ese", "price": 2583.00},
        {"code": "R701", "name": "الترامال (نخب اول)", "company": "ese", "price": 3231.00},
        {"code": "1111", "name": "الف ليلة", "company": "ese", "price": 8181.00},
        {"code": "70152", "name": "الور سبورت (نخب اول)", "company": "ese", "price": 3970.00},
        {"code": "97429", "name": "الوسام", "company": "ese", "price": 4302.00},
        {"code": "97528", "name": "الوسام (نخب اول)", "company": "ese", "price": 6471.00},
        {"code": "2727", "name": "انا الأبيض", "company": "ese", "price": 3208.00},
        {"code": "97541", "name": "انفكتوس", "company": "ese", "price": 3231.00},
        {"code": "70132", "name": "انفكتوس (نخب اول)", "company": "ese", "price": 3573.00},
        {"code": "97520", "name": "انفكتوس باكوربان", "company": "ese", "price": 1935.00},
        {"code": "97502", "name": "انفكتوس باكوربان", "company": "ese", "price": 2799.00},
        {"code": "10190", "name": "انفكتوس فيكتورى الكسير", "company": "ese", "price": 3970.00},
        {"code": "12342", "name": "اوبن", "company": "ese", "price": 2142.00},
        {"code": "OPEN108", "name": "اوبن (نخب اول)", "company": "ese", "price": 2452.00},
        {"code": "21630", "name": "بلاتنيوم", "company": "ese", "price": 3555.00},
        {"code": "97000", "name": "بلاك افغانو", "company": "ese", "price": 6471.00},
        {"code": "97568", "name": "بلاك افغانو", "company": "ese", "price": 7735.00},
        {"code": "70135", "name": "بلاك افغانو (نخب اول)", "company": "ese", "price": 8973.00},
        {"code": "9120", "name": "بلاك اكس اس", "company": "ese", "price": 1935.00},
        {"code": "201", "name": "بلاك ليكزس", "company": "ese", "price": 2322.00},
        {"code": "27", "name": "بلاك ليكزس", "company": "ese", "price": 3015.00},
        {"code": "401401", "name": "بلاك ليكزس", "company": "ese", "price": 3100.00},
        {"code": "97534", "name": "بلاك ليكزس 1000", "company": "ese", "price": 2673.00},
        {"code": "422211", "name": "بوص بوتيل", "company": "ese", "price": 2421.00},
        {"code": "21113", "name": "بی ام دبلیو", "company": "ese", "price": 2601.00},
        {"code": "97517", "name": "بی ام دبلیو (نخب اول)", "company": "ese", "price": 2871.00},
        {"code": "112", "name": "بيكهام", "company": "ese", "price": 2452.00},
        {"code": "9758", "name": "بیور اکس اس", "company": "ese", "price": 2574.00},
        {"code": "97535", "name": "تراب الذهب (نخب اول)", "company": "ese", "price": 2673.00},
        {"code": "97501", "name": "تيرزورا (نخب اول)", "company": "ese", "price": 2452.00},
        {"code": "20913", "name": "ثری جی", "company": "ese", "price": 2367.00},
        {"code": "97589", "name": "ثری جی (نخب اول)", "company": "ese", "price": 2691.00},
        {"code": "97598", "name": "ثيری هيرمس", "company": "ese", "price": 3208.00},
        {"code": "9759", "name": "جورج قرداحی", "company": "ese", "price": 2691.00},
        {"code": "70138", "name": "جورج قرداحی", "company": "ese", "price": 3123.00},
        {"code": "975205", "name": "جیمی شو", "company": "ese", "price": 3100.00},
        {"code": "70103", "name": "جیمی شو (نخب اول)", "company": "ese", "price": 4370.00},
        {"code": "70136", "name": "دراكار اخضر", "company": "ese", "price": 2870.00},
        {"code": "70156", "name": "دراكار اخضر نخب اول)", "company": "ese", "price": 4370.00},
        {"code": "2200E", "name": "درکار", "company": "ese", "price": 2128.00},
        {"code": "97592", "name": "درکار (نخب اول)", "company": "ese", "price": 3231.00},
        {"code": "7011", "name": "دنهل ديزير", "company": "ese", "price": 2241.00},
        {"code": "197012", "name": "دنهل ديزير", "company": "ese", "price": 2673.00},
        {"code": "17903", "name": "دنهل ديزير نخب اول)", "company": "ese", "price": 3141.00},
        {"code": "119E", "name": "دوبامين", "company": "ese", "price": 2331.00},
        {"code": "7701", "name": "ديزير بلو", "company": "ese", "price": 2673.00},
        {"code": "97567", "name": "ديزير بلو 1000", "company": "ese", "price": 2745.00},
        {"code": "12000", "name": "روشاس", "company": "ese", "price": 2128.00},
        {"code": "12001", "name": "روشاس", "company": "ese", "price": 2556.00},
        {"code": "70113", "name": "روشاس (نخب اول)", "company": "ese", "price": 2601.00},
        {"code": "70154", "name": "ريد توباكو (نخب اول)", "company": "ese", "price": 5970.00},
        {"code": "97574", "name": "زارا جولد", "company": "ese", "price": 3231.00},
        {"code": "10920", "name": "سبايس بومب (نخب اول)", "company": "ese", "price": 3970.00},
        {"code": "10005/L", "name": "سلفر سنت", "company": "ese", "price": 2421.00},
        {"code": "10004/R", "name": "سلفر سنت", "company": "ese", "price": 2673.00},
        {"code": "15202", "name": "سلفر سنت (نخب اول)", "company": "ese", "price": 3141.00},
        {"code": "421175", "name": "سوفاج", "company": "ese", "price": 2673.00},
        {"code": "97572", "name": "سوفاج (نخب اول)", "company": "ese", "price": 5211.00},
        {"code": "421176", "name": "سوفاج اكسترا", "company": "ese", "price": 3951.00},
        {"code": "97561", "name": "سوفاج سوبر", "company": "ese", "price": 3573.00},
        {"code": "R108", "name": "سویت کوفی", "company": "ese", "price": 3123.00},
        {"code": "97553", "name": "سویت کوفی نخب اول)", "company": "ese", "price": 5382.00},
        {"code": "10003", "name": "سيجار", "company": "ese", "price": 2511.00},
        {"code": "97593", "name": "سيجار (نخب اول)", "company": "ese", "price": 3771.00},
        {"code": "70118", "name": "شامبيون", "company": "ese", "price": 3573.00},
        {"code": "97557", "name": "شانيل بلو", "company": "ese", "price": 3100.00},
        {"code": "97578", "name": "شانيل بلو (نخب اول)", "company": "ese", "price": 3573.00},
        {"code": "11791", "name": "شروتی", "company": "ese", "price": 2421.00},
        {"code": "70130", "name": "شروتی (نخب اول)", "company": "ese", "price": 3748.00},
        {"code": "97510", "name": "شيلز", "company": "ese", "price": 2844.00},
        {"code": "97550", "name": "غبار الذهب", "company": "ese", "price": 2511.00},
        {"code": "7727/B", "name": "فرذاتشی ایروس", "company": "ese", "price": 2421.00},
        {"code": "110E", "name": "فرداتشی ایروس", "company": "ese", "price": 2583.00},
        {"code": "71028", "name": "فرذاتشی ایروس", "company": "ese", "price": 2961.00},
        {"code": "71026", "name": "فرذاتشی ایروس (نخب اول)", "company": "ese", "price": 3231.00},
        {"code": "70189", "name": "فهرنهايت (نخب اول)", "company": "ese", "price": 3790.00},
        {"code": "2802", "name": "فوياج", "company": "ese", "price": 2196.00},
        {"code": "28313", "name": "فوياج", "company": "ese", "price": 2668.00},
        {"code": "97579", "name": "فوياج (نخب اول)", "company": "ese", "price": 3303.00},
        {"code": "975103", "name": "قصة حب", "company": "ese", "price": 3771.00},
        {"code": "97521", "name": "کرید افنتوس (نخب اول)", "company": "ese", "price": 6084.00},
        {"code": "975802", "name": "کريد بلاك (نخب اول)", "company": "ese", "price": 3231.00},
        {"code": "18460", "name": "کرید سلفر", "company": "ese", "price": 2975.00},
        {"code": "1230", "name": "کنزو", "company": "ese", "price": 2196.00},
        {"code": "2222E", "name": "كول واتر بلو", "company": "ese", "price": 2065.00},
        {"code": "802223", "name": "كول واتر بلو 1000", "company": "ese", "price": 2673.00},
        {"code": "97547", "name": "کی - دولسی جابانا", "company": "ese", "price": 2961.00},
        {"code": "80113", "name": "لابيدوس", "company": "ese", "price": 2421.00},
        {"code": "80114", "name": "لابيدوس", "company": "ese", "price": 2065.00},
        {"code": "975133", "name": "لابيدوس", "company": "ese", "price": 3100.00},
        {"code": "70123", "name": "لابيدوس", "company": "ese", "price": 2970.00},
        {"code": "1000E", "name": "لاكوست اسنشال", "company": "ese", "price": 2421.00},
        {"code": "97599", "name": "لاكوست اسنشال نخب اول)", "company": "ese", "price": 2961.00},
        {"code": "80130", "name": "لاكوست وايت", "company": "ese", "price": 2583.00},
        {"code": "97548", "name": "لاكوست وایت نخب اول)", "company": "ese", "price": 3051.00},
        {"code": "975107", "name": "لانوت", "company": "ese", "price": 3490.00},
        {"code": "10137", "name": "لومال الكسير", "company": "ese", "price": 2970.00},
        {"code": "70157", "name": "لومال الكسير (نخب اول)", "company": "ese", "price": 3970.00},
        {"code": "70192", "name": "ماربرت مان (نخب اول)", "company": "ese", "price": 4390.00},
        {"code": "10118E", "name": "ماربرت مان", "company": "ese", "price": 2970.00},
        {"code": "97533", "name": "مونت بلانك", "company": "ese", "price": 2844.00},
        {"code": "97575", "name": "هاج سنت", "company": "ese", "price": 3231.00},
        {"code": "111E", "name": "هارون", "company": "ese", "price": 8811.00},
        {"code": "15234", "name": "هوجو", "company": "ese", "price": 2322.00},
        {"code": "15235", "name": "هوجو", "company": "ese", "price": 2673.00},
        {"code": "97590", "name": "هوجو سوبر", "company": "ese", "price": 3573.00},
        {"code": "5500", "name": "ون مان شو", "company": "ese", "price": 2421.00},
        {"code": "L/5501", "name": "ون مان شو", "company": "ese", "price": 2601.00},
        {"code": "97564", "name": "ون مان شو (نخب اول)", "company": "ese", "price": 4311.00},
        {"code": "975704", "name": "ون مليون", "company": "ese", "price": 2961.00},
    ]

    try:
        # إنشاء أو الحصول على مورد ese
        if "ese" not in suppliers:
            supplier = Supplier.query.filter_by(name="ese").first()
            if not supplier:
                supplier = Supplier(
                    name="ese",
                    contact_person="غير محدد",
                    phone="غير محدد",
                    address="غير محدد"
                )
                db.session.add(supplier)
                db.session.flush()
            suppliers["ese"] = supplier

        # إضافة العطور - الجزء الثاني
        added_count = 0
        updated_count = 0

        for perfume_data in perfumes_data_part2:
            # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
            existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()

            if existing_perfume:
                # تحديث البيانات الموجودة
                existing_perfume.name = perfume_data["name"]
                existing_perfume.company = perfume_data["company"]
                existing_perfume.price = perfume_data["price"]
                existing_perfume.price_per_kg = perfume_data["price"]
                existing_perfume.is_sold_by_weight = True
                existing_perfume.supplier = suppliers[perfume_data["company"]]
                existing_perfume.updated_at = datetime.now()
                updated_count += 1
                print(f"تم تحديث: {perfume_data['name']} - {perfume_data['company']}")
            else:
                # إضافة عطر جديد
                new_perfume = Perfume(
                    name=perfume_data["name"],
                    company=perfume_data["company"],
                    price=perfume_data["price"],
                    price_per_kg=perfume_data["price"],
                    is_sold_by_weight=True,
                    weight_unit='gram',
                    quantity=999999,  # كمية مفتوحة
                    min_quantity=0,  # بدون حد أدنى
                    sku=perfume_data["code"],
                    supplier=suppliers[perfume_data["company"]],
                    is_active=True
                )
                db.session.add(new_perfume)
                added_count += 1
                print(f"تم إضافة: {perfume_data['name']} - {perfume_data['company']}")

        print(f"\n✅ تم الانتهاء من الجزء الثاني!")
        print(f"📦 عدد العطور المضافة: {added_count}")
        print(f"🔄 عدد العطور المحدثة: {updated_count}")

        return added_count, updated_count

    except Exception as e:
        print(f"❌ حدث خطأ في الجزء الثاني: {str(e)}")
        return 0, 0

if __name__ == "__main__":
    print("🚀 بدء استيراد المجموعة النهائية من العطور...")
    print("=" * 70)

    # استيراد الجزء الأول
    added1, updated1, suppliers = import_ultimate_collection()

    # استيراد الجزء الثاني
    added2, updated2 = import_part2(suppliers)

    # حفظ التغييرات
    with app.app_context():
        try:
            db.session.commit()

            total_added = added1 + added2
            total_updated = updated1 + updated2
            total_processed = total_added + total_updated

            print(f"\n🎉 تم استيراد البيانات بنجاح!")
            print(f"📦 إجمالي العطور المضافة: {total_added}")
            print(f"🔄 إجمالي العطور المحدثة: {total_updated}")
            print(f"📊 إجمالي العطور المعالجة: {total_processed}")
            print(f"🌟 تم إكمال قاعدة البيانات النهائية!")
            print("يمكنك الآن تشغيل النظام ومراجعة جميع العطور.")

        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ في حفظ البيانات: {str(e)}")