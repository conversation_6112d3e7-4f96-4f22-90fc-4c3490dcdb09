#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد عطور دولبرج الإضافية (147 عطر جديد)
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_dolberg_perfumes():
    """استيراد عطور دولبرج الإضافية"""
    
    # بيانات العطور الجديدة من دولبرج
    perfumes_data = [
        {"code": "8910", "name": "أماريج", "company": "دولبرج", "price": 2975.00},
        {"code": "8964", "name": "أمريكانا سبورت", "company": "دولبرج", "price": 3750.00},
        {"code": "5924", "name": "أمور كافيه (جديد)", "company": "دولبرج", "price": 3990.00},
        {"code": "9958", "name": "أمير العود", "company": "دولبرج", "price": 5250.00},
        {"code": "5920", "name": "أنجل شير (جديد)", "company": "دولبرج", "price": 5850.00},
        {"code": "9993", "name": "إنفكتوس", "company": "دولبرج", "price": 2670.00},
        {"code": "8938", "name": "إنفكتوس", "company": "دولبرج", "price": 3880.00},
        {"code": "9951", "name": "إنفكتوس ليجند", "company": "دولبرج", "price": 3800.00},
        {"code": "9913", "name": "أوبن", "company": "دولبرج", "price": 2590.00},
        {"code": "7958", "name": "أوبن", "company": "دولبرج", "price": 3460.00},
        {"code": "7907", "name": "أورجانزا", "company": "دولبرج", "price": 3150.00},
        {"code": "5917", "name": "أوليمبيا", "company": "دولبرج", "price": 2895.00},
        {"code": "9937", "name": "أوليمبيا", "company": "دولبرج", "price": 3625.00},
        {"code": "9973", "name": "إيروس", "company": "دولبرج", "price": 3050.00},
        {"code": "9904", "name": "الحجر الأسود", "company": "دولبرج", "price": 4975.00},
        {"code": "9969", "name": "باد بوي", "company": "دولبرج", "price": 3590.00},
        {"code": "9964", "name": "بامب شيل", "company": "دولبرج", "price": 3190.00},
        {"code": "8996", "name": "باي جيفنشي", "company": "دولبرج", "price": 2790.00},
        {"code": "8919", "name": "باي جيفنشي", "company": "دولبرج", "price": 3590.00},
        {"code": "5936", "name": "برازلیان کراش (جديد)", "company": "دولبرج", "price": 4850.00},
        {"code": "5934", "name": "بربري هير (جديد)", "company": "دولبرج", "price": 3850.00},
        {"code": "5904", "name": "بربري هير", "company": "دولبرج", "price": 3665.00},
        {"code": "9912", "name": "بريتني سبيرز", "company": "دولبرج", "price": 2350.00},
        {"code": "8939", "name": "بريتني سبيرز", "company": "دولبرج", "price": 3890.00},
        {"code": "9983", "name": "بكرات روج", "company": "دولبرج", "price": 5300.00},
        {"code": "7911", "name": "بلاك XS", "company": "دولبرج", "price": 3650.00},
        {"code": "9966", "name": "بلاك أبيوم", "company": "دولبرج", "price": 3490.00},
        {"code": "5930", "name": "بلاك أفغانو (جديد)", "company": "دولبرج", "price": 11000.00},
        {"code": "9945", "name": "بلاك عود", "company": "دولبرج", "price": 11550.00},
        {"code": "8989", "name": "بوس انموشن", "company": "دولبرج", "price": 3225.00},
        {"code": "7963", "name": "بوس انموشن", "company": "دولبرج", "price": 4150.00},
        {"code": "7964", "name": "بوس أورنج", "company": "دولبرج", "price": 3530.00},
        {"code": "7973", "name": "بوس بوتيلد نايت", "company": "دولبرج", "price": 2650.00},
        {"code": "8930", "name": "بولو بلو سبورت", "company": "دولبرج", "price": 2995.00},
        {"code": "5911", "name": "بوما جام", "company": "دولبرج", "price": 2770.00},
        {"code": "8961", "name": "بوما جام", "company": "دولبرج", "price": 4195.00},
        {"code": "9965", "name": "بون بون", "company": "دولبرج", "price": 2795.00},
        {"code": "7977", "name": "بويزون", "company": "دولبرج", "price": 2590.00},
        {"code": "5938", "name": "بي إم دبليو BMW (جديد)", "company": "دولبرج", "price": 3650.00},
        {"code": "9982", "name": "بي إم دبليو BMW", "company": "دولبرج", "price": 3490.00},
        {"code": "5927", "name": "بي ديلشياس أوركارد ستريت", "company": "دولبرج", "price": 4400.00},
        {"code": "5905", "name": "بينك شوجر", "company": "دولبرج", "price": 2560.00},
        {"code": "8926", "name": "بينك شوجر", "company": "دولبرج", "price": 2950.00},
        {"code": "8952", "name": "بينك فرايداي", "company": "دولبرج", "price": 2970.00},
        {"code": "9931", "name": "تندر", "company": "دولبرج", "price": 2950.00},
        {"code": "9935", "name": "توباكو فانيليا", "company": "دولبرج", "price": 3120.00},
        {"code": "7981", "name": "توسكا", "company": "دولبرج", "price": 3415.00},
        {"code": "8958", "name": "تومي بوي", "company": "دولبرج", "price": 3100.00},
        {"code": "8960", "name": "ثري جي", "company": "دولبرج", "price": 3550.00},
        {"code": "7946", "name": "جاجوار جرين", "company": "دولبرج", "price": 3995.00},
        {"code": "9920", "name": "جادور", "company": "دولبرج", "price": 2790.00},
        {"code": "8915", "name": "جوب", "company": "دولبرج", "price": 3750.00},
        {"code": "9967", "name": "جوب نایت فلایت", "company": "دولبرج", "price": 3190.00},
        {"code": "8978", "name": "جوتشي رش", "company": "دولبرج", "price": 2500.00},
        {"code": "7951", "name": "جوتشي رش", "company": "دولبرج", "price": 4695.00},
        {"code": "8934", "name": "جوتشي عود", "company": "دولبرج", "price": 3850.00},
        {"code": "5913", "name": "جود جيرل", "company": "دولبرج", "price": 2500.00},
        {"code": "9968", "name": "جود جيرل", "company": "دولبرج", "price": 3960.00},
        {"code": "5912", "name": "جورج قرداحي", "company": "دولبرج", "price": 2500.00},
        {"code": "9916", "name": "جورج قرداحي", "company": "دولبرج", "price": 3250.00},
        {"code": "7948", "name": "جورج قرداحي", "company": "دولبرج", "price": 4850.00},
        {"code": "5928", "name": "جيمي شو (جديد)", "company": "دولبرج", "price": 3950.00},
        {"code": "5922", "name": "خلطة لطافة (جديد)", "company": "دولبرج", "price": 11000.00},
        {"code": "5919", "name": "خمرة (جديد)", "company": "دولبرج", "price": 5250.00},
        {"code": "8990", "name": "دانهیل دیزایر", "company": "دولبرج", "price": 2875.00},
        {"code": "7970", "name": "دانهیل دیزایر", "company": "دولبرج", "price": 3560.00},
        {"code": "8975", "name": "دانهیل دیزایر", "company": "دولبرج", "price": 4200.00},
        {"code": "7957", "name": "دراكار", "company": "دولبرج", "price": 2875.00},
        {"code": "8991", "name": "دعاء الجنة", "company": "دولبرج", "price": 1625.00},
        {"code": "7997", "name": "دعاء الجنة", "company": "دولبرج", "price": 2250.00},
        {"code": "8942", "name": "دهن عود أبيض", "company": "دولبرج", "price": 7450.00},
        {"code": "7937", "name": "دیزایر بلو", "company": "دولبرج", "price": 3490.00},
        {"code": "5933", "name": "ديلينا إكسكلوسيف (جديد)", "company": "دولبرج", "price": 3750.00},
        {"code": "9972", "name": "ذا وان", "company": "دولبرج", "price": 3590.00},
        {"code": "8904", "name": "راكان", "company": "دولبرج", "price": 4800.00},
        {"code": "9979", "name": "روبيرتو كافالي", "company": "دولبرج", "price": 3490.00},
        {"code": "5906", "name": "روشاس", "company": "دولبرج", "price": 2400.00},
        {"code": "7921", "name": "روشاس", "company": "دولبرج", "price": 3225.00},
        {"code": "8962", "name": "رومانس (رصاصي)", "company": "دولبرج", "price": 3550.00},
        {"code": "8986", "name": "رومبا", "company": "دولبرج", "price": 2295.00},
        {"code": "7980", "name": "رومبا", "company": "دولبرج", "price": 2710.00},
        {"code": "5901", "name": "زارا فور هيم", "company": "دولبرج", "price": 3870.00},
        {"code": "8940", "name": "زهرة الخليج", "company": "دولبرج", "price": 3350.00},
        {"code": "8955", "name": "زهرة الخليج", "company": "دولبرج", "price": 6450.00},
        {"code": "5937", "name": "سادوناسو (جديد)", "company": "دولبرج", "price": 9900.00},
        {"code": "9921", "name": "صن ست فانتازی", "company": "دولبرج", "price": 3900.00},
        {"code": "5903", "name": "سترونجر ويذ يو", "company": "دولبرج", "price": 3550.00},
        {"code": "9947", "name": "سفير الحب", "company": "دولبرج", "price": 9900.00},
        {"code": "5925", "name": "سموكينج هوت (جديد)", "company": "دولبرج", "price": 4500.00},
        {"code": "9963", "name": "سكاندال", "company": "دولبرج", "price": 3225.00},
        {"code": "7938", "name": "سوبر سلطان", "company": "دولبرج", "price": 9690.00},
        {"code": "8985", "name": "سوفاج", "company": "دولبرج", "price": 3790.00},
        {"code": "9971", "name": "سوفاج الكسير", "company": "دولبرج", "price": 5990.00},
        {"code": "9976", "name": "سي ارمانی", "company": "دولبرج", "price": 3490.00},
        {"code": "9977", "name": "سي باشون", "company": "دولبرج", "price": 3490.00},
        {"code": "8992", "name": "سيجار", "company": "دولبرج", "price": 2725.00},
        {"code": "7995", "name": "سيجار", "company": "دولبرج", "price": 4990.00},
        {"code": "9938", "name": "سيكريت شارم", "company": "دولبرج", "price": 3060.00},
        {"code": "8993", "name": "سيلفر سنت", "company": "دولبرج", "price": 2370.00},
        {"code": "7953", "name": "سيلفر سنت", "company": "دولبرج", "price": 3280.00},
        {"code": "8916", "name": "سيلفر شادو", "company": "دولبرج", "price": 3770.00},
        {"code": "9907", "name": "شاليز حريمي", "company": "دولبرج", "price": 3250.00},
        {"code": "8969", "name": "شاليز رجالي", "company": "دولبرج", "price": 3150.00},
        {"code": "8908", "name": "شامبيون دافيدوف", "company": "دولبرج", "price": 3590.00},
        {"code": "7940", "name": "شانيل بلاتنيوم", "company": "دولبرج", "price": 3350.00},
        {"code": "9928", "name": "شانيل بلو", "company": "دولبرج", "price": 3100.00},
        {"code": "9981", "name": "شهرة", "company": "دولبرج", "price": 4250.00},
        {"code": "9914", "name": "شيروتي", "company": "دولبرج", "price": 2670.00},
        {"code": "9901", "name": "شيروتي", "company": "دولبرج", "price": 3570.00},
        {"code": "7989", "name": "شيروتي", "company": "دولبرج", "price": 3860.00},
        {"code": "8967", "name": "عود أصفهان", "company": "دولبرج", "price": 6950.00},
        {"code": "5902", "name": "عود بوكيه", "company": "دولبرج", "price": 3750.00},
        {"code": "8907", "name": "عود شيخة", "company": "دولبرج", "price": 3250.00},
        {"code": "5929", "name": "عود كمبودي (جديد)", "company": "دولبرج", "price": 4950.00},
        {"code": "8953", "name": "غرام", "company": "دولبرج", "price": 3440.00},
        {"code": "8943", "name": "فاراواي", "company": "دولبرج", "price": 2990.00},
        {"code": "9961", "name": "فانتوم", "company": "دولبرج", "price": 3950.00},
        {"code": "5923", "name": "فانيلا سكس (جديد)", "company": "دولبرج", "price": 4500.00},
        {"code": "9933", "name": "فري سكسي ناو", "company": "دولبرج", "price": 3500.00},
        {"code": "9932", "name": "فهرنهایت", "company": "دولبرج", "price": 4700.00},
        {"code": "8945", "name": "فواكه سعودي", "company": "دولبرج", "price": 3025.00},
        {"code": "9997", "name": "فوياج", "company": "دولبرج", "price": 2600.00},
        {"code": "8966", "name": "فوياج", "company": "دولبرج", "price": 4100.00},
        {"code": "9908", "name": "فيراري بلاك", "company": "دولبرج", "price": 3300.00},
        {"code": "9911", "name": "فيفا لاجوسي جلاسي", "company": "دولبرج", "price": 3300.00},
        {"code": "8913", "name": "فيندي", "company": "دولبرج", "price": 3100.00},
        {"code": "8954", "name": "قصر الشوق", "company": "دولبرج", "price": 3300.00},
        {"code": "5907", "name": "كاريزما", "company": "دولبرج", "price": 2600.00},
        {"code": "7933", "name": "كاريزما", "company": "دولبرج", "price": 3665.00},
        {"code": "9998", "name": "كاسيليا", "company": "دولبرج", "price": 2390.00},
        {"code": "7919", "name": "كاسيليا", "company": "دولبرج", "price": 3050.00},
        {"code": "8912", "name": "كروم ليجند", "company": "دولبرج", "price": 3875.00},
        {"code": "9940", "name": "كريد أفنتوس", "company": "دولبرج", "price": 6440.00},
        {"code": "8937", "name": "كريد سيلفر", "company": "دولبرج", "price": 3690.00},
        {"code": "9927", "name": "كريد فايكنج", "company": "دولبرج", "price": 3875.00},
        {"code": "5908", "name": "كريزي لاف", "company": "دولبرج", "price": 2500.00},
        {"code": "8927", "name": "كريزي لاف (غبار الذهب)", "company": "دولبرج", "price": 2950.00},
        {"code": "7942", "name": "كريزي لاف", "company": "دولبرج", "price": 3490.00},
        {"code": "7984", "name": "کشمیر", "company": "دولبرج", "price": 4375.00},
        {"code": "8923", "name": "كلك فلير أخضر", "company": "دولبرج", "price": 2990.00},
        {"code": "9918", "name": "كلك فلير أصفر", "company": "دولبرج", "price": 2750.00},
        {"code": "8909", "name": "كلك فلير أصفر", "company": "دولبرج", "price": 3065.00},
        {"code": "8999", "name": "كلمات", "company": "دولبرج", "price": 3590.00},
        {"code": "7939", "name": "كلوي نرسيز", "company": "دولبرج", "price": 2950.00},
        {"code": "9991", "name": "کنزو", "company": "دولبرج", "price": 2440.00},
        {"code": "7929", "name": "کنزو", "company": "دولبرج", "price": 3490.00},
        {"code": "8941", "name": "کنزو فلاور", "company": "دولبرج", "price": 3280.00},
    ]
    
    with app.app_context():
        try:
            # الحصول على مورد دولبرج أو إنشاؤه
            supplier = Supplier.query.filter_by(name="دولبرج").first()
            if not supplier:
                supplier = Supplier(
                    name="دولبرج",
                    contact_person="غير محدد",
                    phone="غير محدد",
                    address="غير محدد"
                )
                db.session.add(supplier)
                db.session.flush()
            
            # إضافة العطور
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = supplier
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - كود: {perfume_data['code']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=supplier,
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - كود: {perfume_data['code']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")
            print(f"📊 إجمالي العطور المعالجة: {len(perfumes_data)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد عطور دولبرج الإضافية...")
    print("=" * 60)
    
    success = import_dolberg_perfumes()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("يمكنك الآن تشغيل النظام ومراجعة العطور الجديدة.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
