#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام إدارة العطور المبسط
نسخة متكاملة وسهلة التشغيل
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
import sqlite3
import os
from datetime import datetime
import json

app = Flask(__name__)
app.secret_key = 'perfume_store_secret_key_2024'

# إنشاء مجلد البيانات
if not os.path.exists('data'):
    os.makedirs('data')

DB_PATH = 'data/simple_store.db'

def init_db():
    """إنشاء قاعدة البيانات والجداول"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            name TEXT
        )
    ''')
    
    # جدول العطور
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS perfumes (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            company TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL,
            min_quantity INTEGER DEFAULT 5,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الفواتير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY,
            date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total REAL NOT NULL,
            items TEXT NOT NULL
        )
    ''')
    
    # إدراج المستخدم الافتراضي
    cursor.execute('INSERT OR IGNORE INTO users (username, password, name) VALUES (?, ?, ?)',
                   ('ammar', 'ammar', 'عمار - مدير النظام'))
    
    # إدراج بيانات تجريبية
    sample_perfumes = [
        ('عود ملكي', 'العطور الذهبية', 250.0, 50, 10),
        ('ورد دمشقي', 'الروائح الفاخرة', 180.0, 30, 5),
        ('مسك الليل', 'بيت العطور', 320.0, 25, 8),
        ('عنبر أصيل', 'العطور الشرقية', 450.0, 15, 5),
        ('ياسمين شامي', 'الروائح الطبيعية', 200.0, 40, 10)
    ]
    
    cursor.executemany('INSERT OR IGNORE INTO perfumes (name, company, price, quantity, min_quantity) VALUES (?, ?, ?, ?, ?)', 
                       sample_perfumes)
    
    conn.commit()
    conn.close()

def get_db():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

# قوالب HTML مدمجة
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .login-card { background: rgba(255,255,255,0.95); border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .btn-login { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
    </style>
</head>
<body class="d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card login-card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2 class="text-primary">🌸 نظام إدارة العطور</h2>
                            <p class="text-muted">مرحباً بك في نظام إدارة متجر العطور</p>
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">{{ message }}</div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="post">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" name="username" class="form-control" required value="ammar">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" name="password" class="form-control" required value="ammar">
                            </div>
                            <button type="submit" class="btn btn-login text-white w-100">تسجيل الدخول</button>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">المستخدم الافتراضي: ammar / ammar</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .stats-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; }
        .stats-card-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border-radius: 15px; }
        .stats-card-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border-radius: 15px; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .btn { border-radius: 10px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/products">العطور</a>
                <a class="nav-link" href="/invoices">الفواتير</a>
                <a class="nav-link" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <h2 class="mb-4">مرحباً {{ session.user_name }}</h2>
        
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card stats-card text-center p-4">
                    <i class="bi bi-box display-4 mb-3"></i>
                    <h5>عدد العطور</h5>
                    <h2 class="fw-bold">{{ stats.perfumes_count }}</h2>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stats-card-2 text-center p-4">
                    <i class="bi bi-receipt display-4 mb-3"></i>
                    <h5>عدد الفواتير</h5>
                    <h2 class="fw-bold">{{ stats.invoices_count }}</h2>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stats-card-3 text-center p-4">
                    <i class="bi bi-currency-dollar display-4 mb-3"></i>
                    <h5>إجمالي المبيعات</h5>
                    <h2 class="fw-bold">{{ "{:,.0f}".format(stats.total_sales) }}</h2>
                    <small>جنيه مصري</small>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3 mb-3">
                <a href="/products/add" class="btn btn-success w-100 p-3 text-decoration-none">
                    <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
                    إضافة عطر جديد
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/new_invoice" class="btn btn-primary w-100 p-3 text-decoration-none">
                    <i class="bi bi-receipt fs-4 d-block mb-2"></i>
                    فاتورة جديدة
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/products" class="btn btn-info w-100 p-3 text-decoration-none">
                    <i class="bi bi-search fs-4 d-block mb-2"></i>
                    إدارة العطور
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/invoices" class="btn btn-warning w-100 p-3 text-decoration-none">
                    <i class="bi bi-list-ul fs-4 d-block mb-2"></i>
                    عرض الفواتير
                </a>
            </div>
        </div>
        
        {% if low_stock %}
        <div class="alert alert-warning mt-4">
            <h6><i class="bi bi-exclamation-triangle"></i> تنبيه: منتجات منخفضة المخزون</h6>
            <ul class="mb-0">
                {% for item in low_stock %}
                <li>{{ item.name }} - متبقي: {{ item.quantity }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

# Routes
@app.route('/')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db()
    
    # إحصائيات
    perfumes_count = conn.execute('SELECT COUNT(*) FROM perfumes').fetchone()[0]
    invoices_count = conn.execute('SELECT COUNT(*) FROM invoices').fetchone()[0]
    total_sales = conn.execute('SELECT COALESCE(SUM(total), 0) FROM invoices').fetchone()[0]
    
    # منتجات منخفضة المخزون
    low_stock = conn.execute('SELECT * FROM perfumes WHERE quantity <= min_quantity').fetchall()
    
    conn.close()
    
    stats = {
        'perfumes_count': perfumes_count,
        'invoices_count': invoices_count,
        'total_sales': total_sales
    }
    
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats, low_stock=low_stock)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db()
        user = conn.execute('SELECT * FROM users WHERE username = ? AND password = ?', 
                           (username, password)).fetchone()
        conn.close()
        
        if user:
            session['user_id'] = user['id']
            session['user_name'] = user['name']
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/products')
def products():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    search = request.args.get('q', '')
    conn = get_db()

    if search:
        perfumes = conn.execute('SELECT * FROM perfumes WHERE name LIKE ? OR company LIKE ? ORDER BY name',
                               (f'%{search}%', f'%{search}%')).fetchall()
    else:
        perfumes = conn.execute('SELECT * FROM perfumes ORDER BY name').fetchall()

    conn.close()

    products_template = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العطور - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .low-stock { background-color: #f8d7da !important; }
        .table-hover tbody tr:hover { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link active" href="/products">العطور</a>
                <a class="nav-link" href="/invoices">الفواتير</a>
                <a class="nav-link" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3><i class="bi bi-box"></i> إدارة العطور</h3>
            <a href="/products/add" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> إضافة عطر جديد
            </a>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row">
                    <div class="col-md-8 mb-2">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" name="q" class="form-control"
                                   placeholder="بحث بالاسم أو الشركة"
                                   value="{{ request.args.get('q', '') }}">
                        </div>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> بحث
                        </button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="/products" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>اسم العطر</th>
                                <th>الشركة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الحد الأدنى</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for perfume in perfumes %}
                            <tr {% if perfume.quantity <= perfume.min_quantity %}class="low-stock"{% endif %}>
                                <td>
                                    <strong>{{ perfume.name }}</strong>
                                    {% if perfume.quantity <= perfume.min_quantity %}
                                    <span class="badge bg-danger ms-2">مخزون منخفض</span>
                                    {% endif %}
                                </td>
                                <td>{{ perfume.company }}</td>
                                <td><span class="fw-bold text-success">{{ "{:,.2f}".format(perfume.price) }} ج.م</span></td>
                                <td>
                                    <span class="badge {% if perfume.quantity <= perfume.min_quantity %}bg-danger{% else %}bg-success{% endif %}">
                                        {{ perfume.quantity }}
                                    </span>
                                </td>
                                <td>{{ perfume.min_quantity }}</td>
                                <td>
                                    <a href="/products/edit/{{ perfume.id }}" class="btn btn-sm btn-warning me-1">
                                        <i class="bi bi-pencil"></i> تعديل
                                    </a>
                                    <a href="/products/delete/{{ perfume.id }}" class="btn btn-sm btn-danger"
                                       onclick="return confirm('هل أنت متأكد من حذف {{ perfume.name }}؟');">
                                        <i class="bi bi-trash"></i> حذف
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    <i class="bi bi-box display-6 d-block mb-2"></i>
                                    لا توجد عطور مسجلة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    '''

    return render_template_string(products_template, perfumes=perfumes)

@app.route('/products/add', methods=['GET', 'POST'])
def add_product():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        name = request.form['name']
        company = request.form['company']
        price = float(request.form['price'])
        quantity = int(request.form['quantity'])
        min_quantity = int(request.form['min_quantity'])

        conn = get_db()
        conn.execute('INSERT INTO perfumes (name, company, price, quantity, min_quantity) VALUES (?, ?, ?, ?, ?)',
                     (name, company, price, quantity, min_quantity))
        conn.commit()
        conn.close()

        flash('تمت إضافة العطر بنجاح', 'success')
        return redirect(url_for('products'))

    form_template = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عطر جديد - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .required { color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/products">العطور</a>
                <a class="nav-link" href="/invoices">الفواتير</a>
                <a class="nav-link" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h4 class="mb-0">
                            <i class="bi bi-plus-circle"></i> إضافة عطر جديد
                        </h4>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-tag"></i> اسم العطر <span class="required">*</span>
                                    </label>
                                    <input type="text" name="name" class="form-control" required
                                           placeholder="أدخل اسم العطر">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-building"></i> اسم الشركة <span class="required">*</span>
                                    </label>
                                    <input type="text" name="company" class="form-control" required
                                           placeholder="أدخل اسم الشركة المصنعة">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-currency-dollar"></i> السعر (ج.م) <span class="required">*</span>
                                    </label>
                                    <input type="number" name="price" class="form-control" required
                                           step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-box"></i> الكمية المتاحة <span class="required">*</span>
                                    </label>
                                    <input type="number" name="quantity" class="form-control" required
                                           min="0" placeholder="0">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-exclamation-triangle"></i> الحد الأدنى للتنبيه <span class="required">*</span>
                                    </label>
                                    <input type="number" name="min_quantity" class="form-control" required
                                           min="1" value="5" placeholder="5">
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="bi bi-save"></i> حفظ البيانات
                                </button>
                                <a href="/products" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left"></i> العودة لقائمة العطور
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    '''

    return render_template_string(form_template)

@app.route('/products/edit/<int:id>', methods=['GET', 'POST'])
def edit_product(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db()

    if request.method == 'POST':
        name = request.form['name']
        company = request.form['company']
        price = float(request.form['price'])
        quantity = int(request.form['quantity'])
        min_quantity = int(request.form['min_quantity'])

        conn.execute('UPDATE perfumes SET name=?, company=?, price=?, quantity=?, min_quantity=? WHERE id=?',
                     (name, company, price, quantity, min_quantity, id))
        conn.commit()
        conn.close()

        flash('تم تعديل العطر بنجاح', 'success')
        return redirect(url_for('products'))

    perfume = conn.execute('SELECT * FROM perfumes WHERE id = ?', (id,)).fetchone()
    conn.close()

    if not perfume:
        flash('العطر غير موجود', 'error')
        return redirect(url_for('products'))

    edit_template = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل عطر - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .required { color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/products">العطور</a>
                <a class="nav-link" href="/invoices">الفواتير</a>
                <a class="nav-link" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark text-center">
                        <h4 class="mb-0">
                            <i class="bi bi-pencil"></i> تعديل عطر: {{ perfume.name }}
                        </h4>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-tag"></i> اسم العطر <span class="required">*</span>
                                    </label>
                                    <input type="text" name="name" class="form-control" required
                                           value="{{ perfume.name }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-building"></i> اسم الشركة <span class="required">*</span>
                                    </label>
                                    <input type="text" name="company" class="form-control" required
                                           value="{{ perfume.company }}">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-currency-dollar"></i> السعر (ج.م) <span class="required">*</span>
                                    </label>
                                    <input type="number" name="price" class="form-control" required
                                           step="0.01" min="0" value="{{ perfume.price }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-box"></i> الكمية المتاحة <span class="required">*</span>
                                    </label>
                                    <input type="number" name="quantity" class="form-control" required
                                           min="0" value="{{ perfume.quantity }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-exclamation-triangle"></i> الحد الأدنى للتنبيه <span class="required">*</span>
                                    </label>
                                    <input type="number" name="min_quantity" class="form-control" required
                                           min="1" value="{{ perfume.min_quantity }}">
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-warning btn-lg">
                                    <i class="bi bi-save"></i> حفظ التعديلات
                                </button>
                                <a href="/products" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left"></i> العودة لقائمة العطور
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    '''

    return render_template_string(edit_template, perfume=perfume)

@app.route('/products/delete/<int:id>')
def delete_product(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db()
    perfume = conn.execute('SELECT name FROM perfumes WHERE id = ?', (id,)).fetchone()

    if perfume:
        conn.execute('DELETE FROM perfumes WHERE id = ?', (id,))
        conn.commit()
        flash(f'تم حذف العطر "{perfume["name"]}" بنجاح', 'success')
    else:
        flash('العطر غير موجود', 'error')

    conn.close()
    return redirect(url_for('products'))

@app.route('/new_invoice', methods=['GET', 'POST'])
def new_invoice():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    if request.method == 'POST':
        items_data = request.form.get('items')
        if not items_data:
            flash('يجب إضافة منتجات للفاتورة', 'error')
            return redirect(url_for('new_invoice'))

        items = json.loads(items_data)
        total = 0

        conn = get_db()

        # حساب الإجمالي وتحديث المخزون
        for item in items:
            perfume = conn.execute('SELECT * FROM perfumes WHERE id = ?', (item['id'],)).fetchone()
            if perfume and perfume['quantity'] >= item['quantity']:
                total += perfume['price'] * item['quantity']
                # تحديث المخزون
                new_quantity = perfume['quantity'] - item['quantity']
                conn.execute('UPDATE perfumes SET quantity = ? WHERE id = ?', (new_quantity, item['id']))
            else:
                flash(f'كمية غير متاحة للمنتج', 'error')
                conn.close()
                return redirect(url_for('new_invoice'))

        # إنشاء الفاتورة
        conn.execute('INSERT INTO invoices (total, items) VALUES (?, ?)', (total, items_data))
        conn.commit()
        conn.close()

        flash('تم إنشاء الفاتورة بنجاح', 'success')
        return redirect(url_for('invoices'))

    # عرض نموذج الفاتورة
    conn = get_db()
    perfumes = conn.execute('SELECT * FROM perfumes WHERE quantity > 0 ORDER BY name').fetchall()
    conn.close()

    invoice_template = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة جديدة - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .total-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/products">العطور</a>
                <a class="nav-link" href="/invoices">الفواتير</a>
                <a class="nav-link active" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="bi bi-receipt"></i> إنشاء فاتورة بيع جديدة
                </h4>
            </div>
            <div class="card-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="post" id="invoiceForm">
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="bi bi-plus-circle"></i> إضافة منتج للفاتورة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اختر العطر</label>
                                    <select id="perfume" class="form-select">
                                        <option value="">-- اختر العطر --</option>
                                        {% for p in perfumes %}
                                        <option value="{{ p.id }}" data-price="{{ p.price }}" data-quantity="{{ p.quantity }}" data-name="{{ p.name }}" data-company="{{ p.company }}">
                                            {{ p.name }} - {{ p.company }} ({{ "{:,.2f}".format(p.price) }} ج.م) - متوفر: {{ p.quantity }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">الكمية</label>
                                    <input type="number" id="quantity" class="form-control" min="1" value="1" max="1">
                                </div>
                                <div class="col-md-3 mb-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-success w-100" onclick="addItem()">
                                        <i class="bi bi-plus-circle"></i> إضافة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive mb-4">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>اسم العطر</th>
                                    <th>الشركة</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="items-body">
                            </tbody>
                            <tfoot class="total-section">
                                <tr>
                                    <th colspan="4" class="text-end fs-5">الإجمالي الكلي:</th>
                                    <th colspan="2" class="fs-4" id="total">0 ج.م</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <input type="hidden" name="items" id="items-data">

                    <div class="d-grid gap-2">
                        <button type="submit" id="submit-btn" class="btn btn-primary btn-lg" disabled>
                            <i class="bi bi-save"></i> حفظ الفاتورة
                        </button>
                        <a href="/invoices" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> العودة لسجل الفواتير
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let items = [];

        function addItem() {
            const perfumeSelect = document.getElementById('perfume');
            const quantityInput = document.getElementById('quantity');

            if (!perfumeSelect.value) {
                alert('يرجى اختيار عطر');
                return;
            }

            const selectedOption = perfumeSelect.options[perfumeSelect.selectedIndex];
            const perfumeId = parseInt(perfumeSelect.value);
            const quantity = parseInt(quantityInput.value);
            const maxQuantity = parseInt(selectedOption.getAttribute('data-quantity'));

            if (quantity > maxQuantity) {
                alert('الكمية المطلوبة أكبر من المتوفر');
                return;
            }

            // فحص إذا كان المنتج موجود بالفعل
            const existingIndex = items.findIndex(item => item.id === perfumeId);
            if (existingIndex !== -1) {
                items[existingIndex].quantity += quantity;
            } else {
                items.push({
                    id: perfumeId,
                    name: selectedOption.getAttribute('data-name'),
                    company: selectedOption.getAttribute('data-company'),
                    price: parseFloat(selectedOption.getAttribute('data-price')),
                    quantity: quantity
                });
            }

            renderItems();
            perfumeSelect.value = '';
            quantityInput.value = 1;
        }

        function removeItem(index) {
            items.splice(index, 1);
            renderItems();
        }

        function renderItems() {
            const tbody = document.getElementById('items-body');
            tbody.innerHTML = '';
            let total = 0;

            items.forEach((item, index) => {
                const subtotal = item.quantity * item.price;
                total += subtotal;

                tbody.innerHTML += `
                    <tr>
                        <td><strong>${item.name}</strong></td>
                        <td>${item.company}</td>
                        <td><span class="text-success fw-bold">${item.price.toFixed(2)} ج.م</span></td>
                        <td><span class="badge bg-primary">${item.quantity}</span></td>
                        <td><span class="fw-bold">${subtotal.toFixed(2)} ج.م</span></td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(${index})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });

            document.getElementById('total').textContent = total.toFixed(2) + ' ج.م';
            document.getElementById('items-data').value = JSON.stringify(items);
            document.getElementById('submit-btn').disabled = items.length === 0;
        }

        // تحديث الكمية القصوى عند اختيار عطر
        document.getElementById('perfume').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const quantityInput = document.getElementById('quantity');

            if (selectedOption.value) {
                const maxQuantity = selectedOption.getAttribute('data-quantity');
                quantityInput.max = maxQuantity;
                quantityInput.value = Math.min(quantityInput.value, maxQuantity);
            } else {
                quantityInput.max = 1;
                quantityInput.value = 1;
            }
        });
    </script>
</body>
</html>
    '''

    return render_template_string(invoice_template, perfumes=perfumes)

@app.route('/invoices')
def invoices():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    search = request.args.get('q', '')
    conn = get_db()

    if search:
        invoices = conn.execute('SELECT * FROM invoices WHERE id LIKE ? OR date LIKE ? ORDER BY date DESC',
                               (f'%{search}%', f'%{search}%')).fetchall()
    else:
        invoices = conn.execute('SELECT * FROM invoices ORDER BY date DESC').fetchall()

    conn.close()

    invoices_template = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل الفواتير - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .invoice-row:hover { background-color: #f8f9fa; }
        .invoice-total { font-weight: bold; color: #198754; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/products">العطور</a>
                <a class="nav-link active" href="/invoices">الفواتير</a>
                <a class="nav-link" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3><i class="bi bi-receipt"></i> سجل الفواتير</h3>
            <a href="/new_invoice" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> إنشاء فاتورة جديدة
            </a>
        </div>

        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row">
                    <div class="col-md-8 mb-2">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" name="q" class="form-control"
                                   placeholder="بحث برقم الفاتورة أو التاريخ"
                                   value="{{ request.args.get('q', '') }}">
                        </div>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> بحث
                        </button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="/invoices" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>الإجمالي</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr class="invoice-row">
                                <td>
                                    <strong>#{{ invoice.id }}</strong>
                                </td>
                                <td>
                                    <i class="bi bi-calendar3"></i>
                                    {{ invoice.date.split(' ')[0] }}
                                    <br>
                                    <small class="text-muted">{{ invoice.date.split(' ')[1] if ' ' in invoice.date else '' }}</small>
                                </td>
                                <td>
                                    <span class="invoice-total">{{ "{:,.2f}".format(invoice.total) }} ج.م</span>
                                </td>
                                <td>
                                    <a href="/invoices/{{ invoice.id }}" class="btn btn-sm btn-info me-1">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                    <button class="btn btn-sm btn-success" onclick="printInvoice({{ invoice.id }})">
                                        <i class="bi bi-printer"></i> طباعة
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center text-muted py-4">
                                    <i class="bi bi-receipt display-6 d-block mb-2"></i>
                                    لا توجد فواتير مسجلة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function printInvoice(id) {
            window.open('/invoices/' + id + '?print=1', '_blank');
        }
    </script>
</body>
</html>
    '''

    return render_template_string(invoices_template, invoices=invoices)

@app.route('/invoices/<int:id>')
def invoice_detail(id):
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db()
    invoice = conn.execute('SELECT * FROM invoices WHERE id = ?', (id,)).fetchone()

    if not invoice:
        flash('الفاتورة غير موجودة', 'error')
        return redirect(url_for('invoices'))

    # تحليل عناصر الفاتورة
    items_data = json.loads(invoice['items'])
    items = []

    for item_data in items_data:
        perfume = conn.execute('SELECT * FROM perfumes WHERE id = ?', (item_data['id'],)).fetchone()
        if perfume:
            items.append({
                'name': item_data['name'],
                'company': item_data['company'],
                'price': item_data['price'],
                'quantity': item_data['quantity'],
                'subtotal': item_data['price'] * item_data['quantity']
            })

    conn.close()

    is_print = request.args.get('print') == '1'

    detail_template = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الفاتورة #{{ invoice.id }} - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 15px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .invoice-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .total-row { background-color: #f8f9fa; font-weight: bold; }
        @media print {
            .d-print-none { display: none !important; }
            body { background: white !important; }
            .card { border: 1px solid #dee2e6 !important; box-shadow: none !important; }
        }
    </style>
    {% if is_print %}
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
    {% endif %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark mb-4 d-print-none">
        <div class="container">
            <a class="navbar-brand fw-bold" href="/">🌸 نظام إدارة العطور</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/products">العطور</a>
                <a class="nav-link active" href="/invoices">الفواتير</a>
                <a class="nav-link" href="/new_invoice">فاتورة جديدة</a>
            </div>
            <a href="/logout" class="btn btn-outline-light">تسجيل الخروج</a>
        </div>
    </nav>

    <div class="container">
        <div class="card">
            <div class="card-header invoice-header text-center">
                <h4 class="mb-0">
                    <i class="bi bi-receipt"></i> فاتورة بيع رقم #{{ invoice.id }}
                </h4>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6><i class="bi bi-calendar3"></i> معلومات الفاتورة</h6>
                        <p class="mb-1"><strong>رقم الفاتورة:</strong> #{{ invoice.id }}</p>
                        <p class="mb-1"><strong>التاريخ:</strong> {{ invoice.date.split(' ')[0] }}</p>
                        <p class="mb-1"><strong>الوقت:</strong> {{ invoice.date.split(' ')[1] if ' ' in invoice.date else '' }}</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <h6><i class="bi bi-shop"></i> متجر العطور</h6>
                        <p class="mb-1">نظام إدارة العطور</p>
                        <p class="mb-1">فاتورة بيع</p>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>اسم العطر</th>
                                <th>الشركة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in items %}
                            <tr>
                                <td><strong>{{ item.name }}</strong></td>
                                <td>{{ item.company }}</td>
                                <td><span class="text-success">{{ "{:,.2f}".format(item.price) }} ج.م</span></td>
                                <td><span class="badge bg-primary">{{ item.quantity }}</span></td>
                                <td><strong>{{ "{:,.2f}".format(item.subtotal) }} ج.م</strong></td>
                            </tr>
                            {% endfor %}
                            <tr class="total-row">
                                <td colspan="4" class="text-end fs-5">الإجمالي الكلي:</td>
                                <td class="fs-5 text-success"><strong>{{ "{:,.2f}".format(invoice.total) }} ج.م</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="d-print-none">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button onclick="window.print()" class="btn btn-primary w-100">
                                <i class="bi bi-printer"></i> طباعة الفاتورة
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="/invoices/{{ invoice.id }}?print=1" class="btn btn-success w-100" target="_blank">
                                <i class="bi bi-file-pdf"></i> طباعة في نافذة جديدة
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="/invoices" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-arrow-left"></i> العودة للفواتير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    '''

    return render_template_string(detail_template, invoice=invoice, items=items, is_print=is_print)

if __name__ == '__main__':
    init_db()
    print("=" * 50)
    print("🌸 نظام إدارة العطور المبسط")
    print("=" * 50)
    print("🚀 النظام يعمل على: http://127.0.0.1:5000")
    print("👤 المستخدم: ammar")
    print("🔑 كلمة المرور: ammar")
    print("=" * 50)
    app.run(debug=True, host='127.0.0.1', port=5000)
