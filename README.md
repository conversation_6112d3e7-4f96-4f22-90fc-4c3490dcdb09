# 🌸 نظام إدارة العطور

نظام شامل ومتكامل لإدارة متاجر العطور مبني بـ Python Flask مع واجهة عربية حديثة.

## ✨ الميزات الرئيسية

### 📦 إدارة المخزون
- إضافة وتعديل وحذف العطور
- تتبع الكميات والحد الأدنى للتنبيه
- إدارة تواريخ انتهاء الصلاحية
- تتبع حركات المخزون (دخول/خروج/تعديل)
- تنبيهات المخزون المنخفض والمنتهي الصلاحية

### 🧾 نظام الفواتير
- إنشاء فواتير بيع سريعة وسهلة
- طباعة وتصدير الفواتير PDF
- تتبع المبيعات والإيرادات
- ربط الفواتير بالعملاء

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- نظام نقاط الولاء
- تتبع تاريخ المشتريات
- إحصائيات العملاء

### 🚚 إدارة الموردين
- قاعدة بيانات الموردين
- معلومات الاتصال والعناوين
- ربط المنتجات بالموردين

### 📊 التقارير والإحصائيات
- تقارير المبيعات التفصيلية
- أكثر المنتجات مبيعاً
- تقارير المخزون
- رسوم بيانية تفاعلية
- تصدير التقارير Excel

### 👤 إدارة المستخدمين
- نظام أدوار متقدم (مدير/مدير فرع/موظف)
- صلاحيات مخصصة لكل دور
- تتبع نشاط المستخدمين

### 🎨 واجهة المستخدم
- تصميم عربي حديث ومتجاوب
- استخدام Bootstrap 5
- أيقونات Bootstrap Icons
- تأثيرات بصرية جذابة
- دعم الطباعة

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.8+**
- **Flask** - إطار العمل الرئيسي
- **SQLAlchemy** - ORM لقاعدة البيانات
- **Flask-Login** - إدارة جلسات المستخدمين
- **SQLite** - قاعدة البيانات
- **Pandas** - معالجة البيانات
- **ReportLab** - إنشاء ملفات PDF
- **XlsxWriter** - تصدير Excel

### Frontend
- **HTML5** مع دعم العربية
- **Bootstrap 5 RTL** - التصميم المتجاوب
- **Bootstrap Icons** - الأيقونات
- **Chart.js** - الرسوم البيانية
- **CSS3** مع تأثيرات متقدمة
- **JavaScript** للتفاعل

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- نظام تشغيل Windows/Linux/macOS
- 100 ميجابايت مساحة فارغة
- متصفح ويب حديث

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd perfume-store-system
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

### 3. تثبيت التبعيات
```bash
pip install -r requirements.txt
```

### 4. إنشاء قاعدة البيانات
```bash
python create_db.py
```

### 5. تشغيل النظام
```bash
python run.py
```

### 6. فتح المتصفح
افتح المتصفح وانتقل إلى: `http://127.0.0.1:5000`

## 🔐 بيانات تسجيل الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور |
|-------|--------------|-------------|
| مدير النظام | `ammar` | `ammar` |

## 📁 هيكل المشروع

```
perfume-store-system/
├── backend/
│   ├── app.py              # التطبيق الرئيسي
│   ├── models.py           # نماذج قاعدة البيانات
│   ├── static/
│   │   ├── css/
│   │   │   ├── style.css   # التصميم الرئيسي
│   │   │   └── print.css   # تصميم الطباعة
│   │   └── js/
│   │       └── main.js     # JavaScript الرئيسي
│   └── templates/          # قوالب HTML
├── data/
│   └── store.db           # قاعدة البيانات
├── requirements.txt       # التبعيات
├── create_db.py          # إنشاء قاعدة البيانات
├── run.py               # ملف التشغيل
└── README.md           # هذا الملف
```

## 🎯 الاستخدام

### إضافة عطر جديد
1. انتقل إلى "العطور" من القائمة الرئيسية
2. اضغط "إضافة عطر جديد"
3. املأ البيانات المطلوبة
4. اضغط "حفظ البيانات"

### إنشاء فاتورة بيع
1. انتقل إلى "فاتورة جديدة"
2. اختر العطور والكميات
3. راجع الإجمالي
4. اضغط "حفظ الفاتورة"

### عرض التقارير
1. انتقل إلى "إدارة" > "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اضغط "عرض التقرير"

## 🔧 التخصيص

### إضافة مستخدم جديد
1. سجل دخول كمدير نظام
2. انتقل إلى إدارة المستخدمين
3. اضغط "إضافة مستخدم جديد"
4. حدد الدور والصلاحيات

### تخصيص التصميم
- عدل ملف `backend/static/css/style.css`
- أضف ألوان وخطوط مخصصة
- غير الشعار والعلامة التجارية

## 🛡️ الأمان

- تشفير كلمات المرور
- نظام أدوار وصلاحيات
- حماية من SQL Injection
- جلسات آمنة
- تسجيل العمليات

## 📈 الأداء

- قاعدة بيانات محسنة
- فهرسة الجداول
- تحميل سريع للصفحات
- ذاكرة تخزين مؤقت
- ضغط الملفات

## 🐛 استكشاف الأخطاء

### مشكلة في قاعدة البيانات
```bash
python create_db.py
```

### مشكلة في التبعيات
```bash
pip install -r requirements.txt --force-reinstall
```

### مشكلة في التشغيل
تأكد من تفعيل البيئة الافتراضية:
```bash
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/macOS
```

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسل المطور
- راجع الوثائق

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

- Bootstrap Team للتصميم الرائع
- Flask Community للإطار المتميز
- جميع المساهمين في المكتبات المستخدمة

---

**تم تطوير هذا النظام بـ ❤️ لخدمة أصحاب متاجر العطور**
