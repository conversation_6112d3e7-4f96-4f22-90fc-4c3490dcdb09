@echo off
title Perfume Store Management System

echo.
echo ========================================
echo    Perfume Store Management System
echo ========================================
echo.

echo Checking requirements...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed. Please install Python 3.8 or newer
    pause
    exit /b 1
)

echo Python is installed

REM Check virtual environment
if not exist "venv\" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check dependencies
echo Checking dependencies...
pip show flask >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies installed

REM Check database
if not exist "data\store.db" (
    echo Creating database...
    python create_db.py
    if errorlevel 1 (
        echo ERROR: Failed to create database
        pause
        exit /b 1
    )
)

echo Database ready

echo.
echo Starting the system...
echo System will run on: http://127.0.0.1:5000
echo Default user: ammar / ammar
echo.
echo Press Ctrl+C to stop the system
echo ========================================
echo.

REM Start the system
python run.py

echo.
echo System stopped
pause
