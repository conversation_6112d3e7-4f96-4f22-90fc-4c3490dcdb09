// نظام إدارة العطور - ملف JavaScript الرئيسي

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
});

function initializeApp() {
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة الأزرار
    initializeButtons();
    
    // تهيئة البحث
    initializeSearch();
    
    // تهيئة الإحصائيات
    initializeStats();
}

// تهيئة التنبيهات
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            }
        }, 5000);
    });
}

// تهيئة النماذج
function initializeForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('form[novalidate]');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                showFormErrors(form);
            }
            form.classList.add('was-validated');
        });
    });
    
    // تحسين حقول الأرقام
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value < 0) {
                this.value = 0;
            }
        });
    });
    
    // تحسين حقول السعر
    const priceInputs = document.querySelectorAll('input[name="price"]');
    priceInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value) {
                this.value = parseFloat(this.value).toFixed(2);
            }
        });
    });
}

// عرض أخطاء النماذج
function showFormErrors(form) {
    const invalidInputs = form.querySelectorAll(':invalid');
    invalidInputs.forEach(input => {
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            const div = document.createElement('div');
            div.className = 'invalid-feedback';
            div.textContent = getErrorMessage(input);
            input.parentNode.appendChild(div);
        }
    });
}

// الحصول على رسالة الخطأ
function getErrorMessage(input) {
    if (input.validity.valueMissing) {
        return 'هذا الحقل مطلوب';
    }
    if (input.validity.typeMismatch) {
        return 'يرجى إدخال قيمة صحيحة';
    }
    if (input.validity.rangeUnderflow) {
        return `القيمة يجب أن تكون ${input.min} أو أكثر`;
    }
    if (input.validity.rangeOverflow) {
        return `القيمة يجب أن تكون ${input.max} أو أقل`;
    }
    return 'قيمة غير صحيحة';
}

// تهيئة الجداول
function initializeTables() {
    // إضافة تأثيرات التمرير للجداول
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        // إضافة أرقام الصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            if (!row.querySelector('.row-number')) {
                const cell = document.createElement('td');
                cell.className = 'row-number text-muted';
                cell.textContent = index + 1;
                row.insertBefore(cell, row.firstChild);
            }
        });
    });
    
    // تحسين الجداول المتجاوبة
    const responsiveTables = document.querySelectorAll('.table-responsive');
    responsiveTables.forEach(container => {
        container.addEventListener('scroll', function() {
            if (this.scrollLeft > 0) {
                this.classList.add('scrolled');
            } else {
                this.classList.remove('scrolled');
            }
        });
    });
}

// تهيئة الأزرار
function initializeButtons() {
    // إضافة تأثير التحميل للأزرار
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.form && this.form.checkValidity()) {
                showLoading(this);
            }
        });
    });
    
    // تأكيد الحذف
    const deleteButtons = document.querySelectorAll('a[href*="/delete/"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            if (!confirm('هل أنت متأكد من الحذف؟ لا يمكن التراجع عن هذا الإجراء.')) {
                event.preventDefault();
            }
        });
    });
}

// عرض حالة التحميل
function showLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner"></span> جاري المعالجة...';
    button.disabled = true;
    
    // إعادة تعيين الزر بعد 10 ثوان كحد أقصى
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 10000);
}

// تهيئة البحث
function initializeSearch() {
    const searchInputs = document.querySelectorAll('input[name="q"]');
    searchInputs.forEach(input => {
        // البحث التلقائي أثناء الكتابة
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
        
        // إضافة أيقونة البحث
        if (!input.parentNode.querySelector('.search-icon')) {
            const icon = document.createElement('i');
            icon.className = 'bi bi-search search-icon';
            input.parentNode.style.position = 'relative';
            input.parentNode.appendChild(icon);
        }
    });
}

// تهيئة الإحصائيات
function initializeStats() {
    // تحريك الأرقام في كروت الإحصائيات
    const statNumbers = document.querySelectorAll('.display-4');
    statNumbers.forEach(element => {
        const finalValue = parseInt(element.textContent.replace(/[^\d]/g, ''));
        if (!isNaN(finalValue)) {
            animateNumber(element, 0, finalValue, 1000);
        }
    });
}

// تحريك الأرقام
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const originalText = element.textContent;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = Math.floor(start + (end - start) * progress);
        
        // الحفاظ على التنسيق الأصلي
        if (originalText.includes(',')) {
            element.textContent = currentValue.toLocaleString('ar-EG');
        } else {
            element.textContent = currentValue;
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// وظائف مساعدة للفواتير
function updateInvoiceTotal() {
    const items = document.querySelectorAll('#items-body tr');
    let total = 0;
    
    items.forEach(row => {
        const price = parseFloat(row.cells[2].textContent.replace(/[^\d.]/g, ''));
        const quantity = parseInt(row.cells[3].querySelector('input').value);
        const subtotal = price * quantity;
        
        row.cells[4].innerHTML = `<span class="fw-bold">${subtotal.toFixed(2)} ج.م</span>`;
        total += subtotal;
    });
    
    document.getElementById('total').textContent = total.toFixed(2) + ' ج.م';
    document.getElementById('submit-btn').disabled = items.length === 0;
}

// تحديث كمية المنتج في الفاتورة
function updateQuantity(index, newQuantity) {
    const row = document.querySelectorAll('#items-body tr')[index];
    if (row) {
        const maxQuantity = parseInt(row.dataset.maxQuantity);
        if (newQuantity > 0 && newQuantity <= maxQuantity) {
            updateInvoiceTotal();
        } else {
            // إعادة تعيين القيمة السابقة
            const input = row.cells[3].querySelector('input');
            input.value = Math.min(Math.max(1, newQuantity), maxQuantity);
        }
    }
}

// تصدير البيانات
function exportData(type, format) {
    const url = `/${type}/export?format=${format}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = `${type}_${new Date().toISOString().split('T')[0]}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// طباعة الفاتورة
function printInvoice() {
    // إخفاء العناصر غير المطلوبة للطباعة
    const printElements = document.querySelectorAll('.d-print-none');
    printElements.forEach(el => el.style.display = 'none');
    
    window.print();
    
    // إعادة إظهار العناصر
    printElements.forEach(el => el.style.display = '');
}

// إضافة تأثيرات بصرية
function addVisualEffects() {
    // تأثير الموجة عند النقر
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn')) {
            createRipple(e);
        }
    });
}

// إنشاء تأثير الموجة
function createRipple(event) {
    const button = event.target;
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    button.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// تهيئة التأثيرات البصرية
addVisualEffects();

// وظائف الإشعارات
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// حفظ البيانات محلياً
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (e) {
        console.warn('لا يمكن حفظ البيانات محلياً:', e);
    }
}

// استرجاع البيانات المحفوظة محلياً
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.warn('لا يمكن استرجاع البيانات المحفوظة:', e);
        return null;
    }
}

// تصدير الوظائف للاستخدام العام
window.PerfumeStore = {
    showNotification,
    exportData,
    printInvoice,
    updateQuantity,
    saveToLocalStorage,
    getFromLocalStorage
};
