<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>

<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="bi bi-boxes"></i> إدارة المخزون</h3>
    <div>
      <a href="/stock/adjustment" class="btn btn-warning me-2">
        <i class="bi bi-gear"></i> تعديل المخزون
      </a>
      <a href="/stock/movements" class="btn btn-info">
        <i class="bi bi-arrow-up-down"></i> حركات المخزون
      </a>
    </div>
  </div>
  
  <!-- تنبيهات المخزون -->
  {% if low_stock or expired or expiring_soon %}
  <div class="row mb-4">
    {% if low_stock %}
    <div class="col-md-4 mb-3">
      <div class="alert alert-danger">
        <h6><i class="bi bi-exclamation-triangle"></i> مخزون منخفض ({{ low_stock|length }})</h6>
        <ul class="mb-0">
          {% for item in low_stock[:3] %}
          <li>{{ item.name }} - متبقي: {{ item.quantity }}</li>
          {% endfor %}
          {% if low_stock|length > 3 %}
          <li><small>و {{ low_stock|length - 3 }} منتجات أخرى...</small></li>
          {% endif %}
        </ul>
      </div>
    </div>
    {% endif %}
    
    {% if expired %}
    <div class="col-md-4 mb-3">
      <div class="alert alert-danger">
        <h6><i class="bi bi-calendar-x"></i> منتهية الصلاحية ({{ expired|length }})</h6>
        <ul class="mb-0">
          {% for item in expired[:3] %}
          <li>{{ item.name }} - انتهت: {{ item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else 'غير محدد' }}</li>
          {% endfor %}
          {% if expired|length > 3 %}
          <li><small>و {{ expired|length - 3 }} منتجات أخرى...</small></li>
          {% endif %}
        </ul>
      </div>
    </div>
    {% endif %}
    
    {% if expiring_soon %}
    <div class="col-md-4 mb-3">
      <div class="alert alert-warning">
        <h6><i class="bi bi-clock"></i> قريبة الانتهاء ({{ expiring_soon|length }})</h6>
        <ul class="mb-0">
          {% for item in expiring_soon[:3] %}
          <li>{{ item.name }} - تنتهي: {{ item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else 'غير محدد' }}</li>
          {% endfor %}
          {% if expiring_soon|length > 3 %}
          <li><small>و {{ expiring_soon|length - 3 }} منتجات أخرى...</small></li>
          {% endif %}
        </ul>
      </div>
    </div>
    {% endif %}
  </div>
  {% endif %}
  
  <!-- إحصائيات سريعة -->
  <div class="row mb-4">
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card">
        <div class="card-body">
          <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
          <h5 class="card-title">مخزون منخفض</h5>
          <p class="display-6 fw-bold">{{ low_stock|length }}</p>
          <small>منتج</small>
        </div>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card-2">
        <div class="card-body">
          <i class="bi bi-calendar-x display-4 mb-3"></i>
          <h5 class="card-title">منتهية الصلاحية</h5>
          <p class="display-6 fw-bold">{{ expired|length }}</p>
          <small>منتج</small>
        </div>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card-3">
        <div class="card-body">
          <i class="bi bi-clock display-4 mb-3"></i>
          <h5 class="card-title">قريبة الانتهاء</h5>
          <p class="display-6 fw-bold">{{ expiring_soon|length }}</p>
          <small>منتج</small>
        </div>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="card text-center shadow stats-card">
        <div class="card-body">
          <i class="bi bi-arrow-up-down display-4 mb-3"></i>
          <h5 class="card-title">آخر الحركات</h5>
          <p class="display-6 fw-bold">{{ recent_movements|length }}</p>
          <small>حركة</small>
        </div>
      </div>
    </div>
  </div>
  
  <!-- آخر حركات المخزون -->
  {% if recent_movements %}
  <div class="card shadow mb-4">
    <div class="card-header bg-info text-white">
      <h5 class="mb-0"><i class="bi bi-clock-history"></i> آخر حركات المخزون</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>المنتج</th>
              <th>نوع الحركة</th>
              <th>الكمية</th>
              <th>السبب</th>
              <th>التاريخ</th>
              <th>المستخدم</th>
            </tr>
          </thead>
          <tbody>
            {% for movement in recent_movements %}
            <tr>
              <td><strong>{{ movement.perfume.name }}</strong></td>
              <td>
                <span class="badge bg-{% if movement.movement_type == 'in' %}success{% elif movement.movement_type == 'out' %}danger{% else %}warning{% endif %}">
                  {% if movement.movement_type == 'in' %}دخول{% elif movement.movement_type == 'out' %}خروج{% else %}تعديل{% endif %}
                </span>
              </td>
              <td>
                <span class="{% if movement.quantity > 0 %}text-success{% else %}text-danger{% endif %} fw-bold">
                  {{ movement.quantity }}
                </span>
              </td>
              <td>{{ movement.reason or '-' }}</td>
              <td>
                <small>{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
              </td>
              <td>{{ movement.user.username if movement.user else '-' }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="text-center">
        <a href="/stock/movements" class="btn btn-outline-info">
          <i class="bi bi-eye"></i> عرض جميع الحركات
        </a>
      </div>
    </div>
  </div>
  {% endif %}
  
  <!-- إجراءات سريعة -->
  <div class="row">
    <div class="col-12">
      <h5 class="mb-3"><i class="bi bi-lightning"></i> إجراءات سريعة</h5>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/products/add" class="btn btn-outline-success w-100 p-3">
        <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
        إضافة منتج جديد
      </a>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/stock/adjustment" class="btn btn-outline-warning w-100 p-3">
        <i class="bi bi-gear fs-4 d-block mb-2"></i>
        تعديل المخزون
      </a>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/products?q=" class="btn btn-outline-info w-100 p-3">
        <i class="bi bi-search fs-4 d-block mb-2"></i>
        البحث في المنتجات
      </a>
    </div>
    <div class="col-md-3 mb-3">
      <a href="/products/export" class="btn btn-outline-primary w-100 p-3">
        <i class="bi bi-download fs-4 d-block mb-2"></i>
        تصدير المخزون
      </a>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
