#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لاستيراد العطور من الموردين الجدد
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume, Supplier
db.init_app(app)

def import_new_suppliers():
    """استيراد العطور من الموردين الجدد"""
    
    # بيانات العطور من الموردين الجدد
    perfumes_data = [
        # دولبرج - منتجات إضافية
        {"code": "1020", "name": "بابل جم (صناعي)", "company": "دولبرج", "price": 3100.00},
        {"code": "1021", "name": "أوركيد و جوزهند", "company": "دولبرج", "price": 1700.00},
        {"code": "1022", "name": "جوزهند", "company": "دولبرج", "price": 1575.00},
        {"code": "1023", "name": "آیس کول", "company": "دولبرج", "price": 1825.00},
        {"code": "1024", "name": "فراولة (صناعي)", "company": "دولبرج", "price": 1100.00},
        {"code": "1025", "name": "أوركيد", "company": "دولبرج", "price": 1375.00},
        {"code": "1026", "name": "صني ليمون (صناعي)", "company": "دولبرج", "price": 1500.00},
        {"code": "1027", "name": "بابايا", "company": "دولبرج", "price": 1825.00},
        {"code": "1028", "name": "توتي فروتي (صناعي)", "company": "دولبرج", "price": 1200.00},
        {"code": "1029", "name": "نیو مون (صناعي)", "company": "دولبرج", "price": 1400.00},
        {"code": "1030", "name": "سبرنج (صناعي)", "company": "دولبرج", "price": 1500.00},
        {"code": "1031", "name": "كرييتف ليمون (صناعي)", "company": "دولبرج", "price": 1690.00},
        {"code": "1032", "name": "برسيل (صناعي)", "company": "دولبرج", "price": 3150.00},
        {"code": "1033", "name": "برسيل ألوان (صناعي)", "company": "دولبرج", "price": 2250.00},
        {"code": "1034", "name": "برسيل (صناعي)", "company": "دولبرج", "price": 2100.00},
        {"code": "1035", "name": "داوني (صناعي)", "company": "دولبرج", "price": 1990.00},
        {"code": "1036", "name": "كمفورت بلو (صناعي)", "company": "دولبرج", "price": 1590.00},
        {"code": "1037", "name": "كمفورت بينك (صناعي)", "company": "دولبرج", "price": 1850.00},
        {"code": "1038", "name": "ليمون بريل (صناعي)", "company": "دولبرج", "price": 1990.00},
        
        # نيو برفيوم
        {"code": "N1228", "name": "ليبر ايف سان لوران", "company": "نيو برفيوم", "price": 3690.00},
        {"code": "N1177", "name": "عود شيخة", "company": "نيو برفيوم", "price": 3990.00},
        {"code": "N1166", "name": "كريد أفنتوس كولون", "company": "نيو برفيوم", "price": 4170.00},
        {"code": "N1167", "name": "مسك أبيض", "company": "نيو برفيوم", "price": 2625.00},
        {"code": "N1120", "name": "جاكومو", "company": "نيو برفيوم", "price": 2690.00},
        
        # بيل
        {"code": "470", "name": "انستانت کراش", "company": "بيل", "price": 4750.00},
        {"code": "813972", "name": "اسكادا اسبيشيال", "company": "بيل", "price": 3350.00},
        {"code": "807311", "name": "اكوا ديجيو", "company": "بيل", "price": 3775.00},
        {"code": "806750", "name": "انجل", "company": "بيل", "price": 4125.00},
        {"code": "806926", "name": "أميركانا اسبورت", "company": "بيل", "price": 4125.00},
        {"code": "801876", "name": "باشا كارتير", "company": "بيل", "price": 3950.00},
        {"code": "5469", "name": "بلاك ليكزس", "company": "بيل", "price": 3625.00},
        {"code": "797705", "name": "بالد سیرینی", "company": "بيل", "price": 4500.00},
        {"code": "813929", "name": "بدی بربری", "company": "بيل", "price": 4150.00},
        {"code": "810836", "name": "بنك شوجر", "company": "بيل", "price": 2925.00},
        {"code": "791661", "name": "جان بول", "company": "بيل", "price": 4375.00},
        {"code": "810787", "name": "جس رجالي", "company": "بيل", "price": 4000.00},
        {"code": "804721", "name": "جوب نایت فلایت", "company": "بيل", "price": 3575.00},
        {"code": "790745", "name": "جوتشی رش", "company": "بيل", "price": 3300.00},
        {"code": "803319", "name": "دانهل برسويت", "company": "بيل", "price": 4200.00},
        {"code": "806171", "name": "دانهل لندن", "company": "بيل", "price": 3575.00},
        {"code": "7000184", "name": "دولسن اند جابانا 3", "company": "بيل", "price": 4625.00},
        {"code": "816052", "name": "دولسن اند جابانا ريد", "company": "بيل", "price": 3850.00},
        {"code": "804474", "name": "روشاز", "company": "بيل", "price": 3100.00},
        {"code": "806672", "name": "سلفر راين", "company": "بيل", "price": 4350.00},
        {"code": "7000208", "name": "سوبريم بوكيت", "company": "بيل", "price": 4350.00},
        {"code": "7500212", "name": "سوفاج", "company": "بيل", "price": 4750.00},
        {"code": "800145", "name": "شامبيون دايفي دوف", "company": "بيل", "price": 3850.00},
        {"code": "803553", "name": "عنبر الملوك", "company": "بيل", "price": 5100.00},
        {"code": "807637", "name": "عود عمانی", "company": "بيل", "price": 3750.00},
        {"code": "7500234", "name": "عود وود", "company": "بيل", "price": 6600.00},
        {"code": "808243", "name": "فل سعودی مرکز", "company": "بيل", "price": 3000.00},
        {"code": "806632", "name": "فلاور جوتشی", "company": "بيل", "price": 3475.00},
        {"code": "802516", "name": "فهرنهایت", "company": "بيل", "price": 3850.00},
        {"code": "806876", "name": "فوياج", "company": "بيل", "price": 3700.00},
        {"code": "804036", "name": "کریزی لاف", "company": "بيل", "price": 2650.00},
        {"code": "789748", "name": "کنزو", "company": "بيل", "price": 4050.00},
        {"code": "800642", "name": "لاكوست استنشيال", "company": "بيل", "price": 3300.00},
        {"code": "806973", "name": "مسك الكعبة", "company": "بيل", "price": 4375.00},
        {"code": "794219", "name": "هريرا 212", "company": "بيل", "price": 3600.00},
        {"code": "807996", "name": "وصال", "company": "بيل", "price": 4250.00},
        {"code": "7500149", "name": "سلفر سنت", "company": "بيل", "price": 2900.00},
        {"code": "860", "name": "زارا جولد", "company": "بيل", "price": 3300.00},
        {"code": "7500623", "name": "ايروس فرزاتشي", "company": "بيل", "price": 3550.00},
        {"code": "794", "name": "كاتي بيري", "company": "بيل", "price": 2950.00},
        {"code": "7800391", "name": "بی ام دبلیو", "company": "بيل", "price": 3400.00},
        {"code": "7500407", "name": "استرونجر ويز يو", "company": "بيل", "price": 3985.00},
        {"code": "7800392", "name": "استرونجر ویز می", "company": "بيل", "price": 3600.00},
        {"code": "857", "name": "اسكندال حريمي", "company": "بيل", "price": 3100.00},
        {"code": "2097", "name": "شامبين تویست باث&بودی", "company": "بيل", "price": 2975.00},
        {"code": "2490", "name": "لابيل جان بول", "company": "بيل", "price": 3450.00},
        {"code": "4339", "name": "ورم فانيليا (باث & بودی)", "company": "بيل", "price": 2675.00},
        {"code": "659", "name": "ايه ثاوثاند ویشیز (باث&بادی)", "company": "بيل", "price": 3050.00},
        {"code": "480", "name": "الترامال", "company": "بيل", "price": 3650.00},
        {"code": "726", "name": "بوص ذا سنت رجالي", "company": "بيل", "price": 3800.00},
        {"code": "240", "name": "توباكو فانيليا", "company": "بيل", "price": 4750.00},
        {"code": "851", "name": "جود جيرل", "company": "بيل", "price": 4050.00},
        {"code": "724", "name": "وكيكي كوكو نت (باث&بودی)", "company": "بيل", "price": 2775.00},
        {"code": "809163", "name": "وصال", "company": "بيل", "price": 3200.00},
        {"code": "2000", "name": "ذا وان رجالي", "company": "بيل", "price": 5500.00},
        {"code": "7500694", "name": "بلو شانيل", "company": "بيل", "price": 3175.00},
        {"code": "6747", "name": "جراس فلاور", "company": "بيل", "price": 4650.00},
        {"code": "6699", "name": "الترامارين", "company": "بيل", "price": 3075.00},
        {"code": "500234", "name": "عود وود", "company": "بيل", "price": 6600.00},
        {"code": "531", "name": "مسك فانيليا", "company": "بيل", "price": 3850.00},
        {"code": "7800558", "name": "بلاك فانتوم (كيليان)", "company": "بيل", "price": 8500.00},
        {"code": "1112", "name": "کایلی يم بستاشيو جيلاتو", "company": "بيل", "price": 4750.00},
        {"code": "1114", "name": "کایلی کابرى ليمون شوجر", "company": "بيل", "price": 4850.00},
        {"code": "581", "name": "حياتي جولد الكسير", "company": "بيل", "price": 4250.00},
        {"code": "862", "name": "لامال الكسير", "company": "بيل", "price": 4000.00},
        {"code": "1126", "name": "هيبسكوس مهاجاد", "company": "بيل", "price": 10500.00},
        {"code": "7800572", "name": "ایما جنیشن", "company": "بيل", "price": 6500.00},
        {"code": "1129", "name": "توباكو فانيليا", "company": "بيل", "price": 6000.00},
        
        # اروماتيك
        {"code": "52875", "name": "بلو شانيل", "company": "اروماتيك", "price": 4200.00},
        {"code": "50754", "name": "تاتش اوف بنك", "company": "اروماتيك", "price": 3900.00},
        {"code": "50999", "name": "جاجوار جرين", "company": "اروماتيك", "price": 3760.00},
        {"code": "50548", "name": "جفنشي بلو لیبل", "company": "اروماتيك", "price": 3705.00},
        {"code": "19021", "name": "جوب نایت فلایت", "company": "اروماتيك", "price": 4500.00},
        {"code": "53229", "name": "جيلتى جوتشي", "company": "اروماتيك", "price": 3780.00},
        {"code": "20969", "name": "دانهیل دیزایر", "company": "اروماتيك", "price": 4350.00},
        {"code": "65925", "name": "دانهیل دیزایر اکستریم", "company": "اروماتيك", "price": 3100.00},
        {"code": "21880", "name": "دیزایر بلو", "company": "اروماتيك", "price": 4200.00},
        {"code": "51366", "name": "سكسي 212", "company": "اروماتيك", "price": 3470.00},
        {"code": "19003", "name": "روشاذ", "company": "اروماتيك", "price": 4100.00},
        {"code": "51060", "name": "سلفر بلاك", "company": "اروماتيك", "price": 4600.00},
        {"code": "59037", "name": "سوفاج", "company": "اروماتيك", "price": 3700.00},
        {"code": "20706", "name": "جادور", "company": "اروماتيك", "price": 3530.00},
        {"code": "50411", "name": "فيندي", "company": "اروماتيك", "price": 4300.00},
        {"code": "51012", "name": "هوجو انرجی", "company": "اروماتيك", "price": 4100.00},
        {"code": "51958", "name": "وان میلیون", "company": "اروماتيك", "price": 3990.00},
        {"code": "82202", "name": "مضاوي", "company": "اروماتيك", "price": 4900.00},
        {"code": "76831", "name": "عود فور جرينتس", "company": "اروماتيك", "price": 5700.00},
        
        # جنرال
        {"code": "12918", "name": "استرونجر انتنسلی", "company": "جنرال", "price": 4200.00},
        {"code": "10517", "name": "اسكلبشر", "company": "جنرال", "price": 3760.00},
        {"code": "12869", "name": "اسکندال رجالي", "company": "جنرال", "price": 3700.00},
        {"code": "4837", "name": "اسمیاکی", "company": "جنرال", "price": 4380.00},
        {"code": "4952", "name": "الترامارين", "company": "جنرال", "price": 3170.00},
        {"code": "8169", "name": "اوبن", "company": "جنرال", "price": 5600.00},
        {"code": "6611", "name": "بلاتنيوم", "company": "جنرال", "price": 4300.00},
        {"code": "9948", "name": "بلاك كود", "company": "جنرال", "price": 4100.00},
        {"code": "9646", "name": "جاكومو", "company": "جنرال", "price": 4800.00},
        {"code": "3437", "name": "جوب", "company": "جنرال", "price": 4000.00},
        {"code": "10479", "name": "جوب جامب", "company": "جنرال", "price": 4680.00},
        {"code": "9451", "name": "دانهل إيدشن", "company": "جنرال", "price": 5500.00},
        {"code": "5133", "name": "دراكار", "company": "جنرال", "price": 4500.00},
        {"code": "9524", "name": "دراكار اصفر", "company": "جنرال", "price": 4000.00},
        {"code": "10341", "name": "رومبا", "company": "جنرال", "price": 3675.00},
        {"code": "12719", "name": "سي باشون (آرمانی احمر)", "company": "جنرال", "price": 3950.00},
        {"code": "3747", "name": "شیروتی", "company": "جنرال", "price": 4350.00},
        {"code": "4978", "name": "صندل", "company": "جنرال", "price": 2150.00},
        {"code": "12870", "name": "فانتوم", "company": "جنرال", "price": 3900.00},
        {"code": "3414", "name": "كارولينا هريرا", "company": "جنرال", "price": 3100.00},
        {"code": "5134", "name": "لابيدوس", "company": "جنرال", "price": 4600.00},
        {"code": "10599", "name": "مسك انجليزي 1000", "company": "جنرال", "price": 2170.00},
        {"code": "5224", "name": "مسك انجليزي 2000", "company": "جنرال", "price": 5400.00},
        {"code": "4818", "name": "هوجو", "company": "جنرال", "price": 4460.00},
        {"code": "10849", "name": "هوجو امبر بلد سیرینی", "company": "جنرال", "price": 3500.00},
        {"code": "3404", "name": "وان مان شو 04", "company": "جنرال", "price": 4100.00},
        {"code": "1005G", "name": "وان مان شو 05", "company": "جنرال", "price": 5100.00},
        {"code": "1687", "name": "ورد بلدی", "company": "جنرال", "price": 2850.00},
        {"code": "12721", "name": "وان مليون لاكي", "company": "جنرال", "price": 3700.00},
        {"code": "12864", "name": "اومبر نومید", "company": "جنرال", "price": 3600.00},

        # تكنو فلاور
        {"code": "21582", "name": "اسكلبشر", "company": "تكنو فلاور", "price": 2680.00},
        {"code": "3339", "name": "أترنتي اصفر", "company": "تكنو فلاور", "price": 4350.00},
        {"code": "9228", "name": "فيم", "company": "تكنو فلاور", "price": 3170.00},
        {"code": "10861", "name": "تمر حنه", "company": "تكنو فلاور", "price": 2580.00},
        {"code": "8729", "name": "فوريو", "company": "تكنو فلاور", "price": 4400.00},
        {"code": "22342", "name": "اسكادا كندى لاف", "company": "تكنو فلاور", "price": 3900.00},
        {"code": "46586", "name": "عود ابيض", "company": "تكنو فلاور", "price": 4200.00},
        {"code": "2649", "name": "كوكو شانيل", "company": "تكنو فلاور", "price": 3600.00},
        {"code": "77063", "name": "ون مليون رويال", "company": "تكنو فلاور", "price": 4500.00},
        {"code": "1067", "name": "كول ووتر بلو", "company": "تكنو فلاور", "price": 2900.00},
        {"code": "8409T", "name": "مسك الطهاره كافيه", "company": "تكنو فلاور", "price": 4750.00},
        {"code": "6232T", "name": "مسك الطهاره بيبي", "company": "تكنو فلاور", "price": 4350.00},
        {"code": "6760", "name": "الترمال", "company": "تكنو فلاور", "price": 2980.00},
        {"code": "7884", "name": "انفكتوس فكتوري الكسير", "company": "تكنو فلاور", "price": 3900.00},
        {"code": "6315", "name": "مخلط اماراتی", "company": "تكنو فلاور", "price": 3200.00},
        {"code": "2220", "name": "ليبر بلاتين", "company": "تكنو فلاور", "price": 2900.00},
        {"code": "2826T", "name": "مسك الطهاره", "company": "تكنو فلاور", "price": 4950.00},
        {"code": "454", "name": "هوجو بوص سنت", "company": "تكنو فلاور", "price": 3680.00},
        {"code": "6050", "name": "مليون جولد وومن", "company": "تكنو فلاور", "price": 5300.00},
        {"code": "6274", "name": "مليون جولد اتننس", "company": "تكنو فلاور", "price": 5600.00},
        {"code": "518", "name": "جود جيرل بلاش الكسير", "company": "تكنو فلاور", "price": 3650.00},
        {"code": "1042", "name": "هیلسون فالی", "company": "تكنو فلاور", "price": 3800.00},
        {"code": "6298", "name": "مسك الطهارة غزل البنات", "company": "تكنو فلاور", "price": 4300.00},
        {"code": "5357", "name": "مسك الطهارة غرام", "company": "تكنو فلاور", "price": 4300.00},
        {"code": "6372", "name": "استرونجر ویز می", "company": "تكنو فلاور", "price": 3400.00},
        {"code": "6373", "name": "امير العود", "company": "تكنو فلاور", "price": 3900.00},
        {"code": "3624", "name": "مای سیلف", "company": "تكنو فلاور", "price": 2900.00},
        {"code": "8056", "name": "یارا کاندی", "company": "تكنو فلاور", "price": 3980.00},
        {"code": "8845", "name": "ایروس فرزاتشی نجم", "company": "تكنو فلاور", "price": 3950.00},
        {"code": "5842", "name": "سول دى جانيرو 40", "company": "تكنو فلاور", "price": 3980.00},

        # Apa
        {"code": "1100", "name": "اديدس موفيز", "company": "Apa", "price": 2585.00},
        {"code": "2091", "name": "اسكادا تاج", "company": "Apa", "price": 1980.00},
        {"code": "2011", "name": "اسكادا كولكشن", "company": "Apa", "price": 2435.00},
        {"code": "1098", "name": "اسكلبشر", "company": "Apa", "price": 2350.00},
        {"code": "2040", "name": "اسكيب", "company": "Apa", "price": 2105.00},
        {"code": "4026", "name": "اكوا ديجيو", "company": "Apa", "price": 3455.00},
        {"code": "1026A", "name": "اكوا ديجيو", "company": "Apa", "price": 2435.00},
        {"code": "1048", "name": "اليرسبورت", "company": "Apa", "price": 2560.00},
        {"code": "1097", "name": "انفكتوس", "company": "Apa", "price": 2430.00},
        {"code": "1058", "name": "اوبن", "company": "Apa", "price": 2630.00},
        {"code": "2087", "name": "ايفوريا حريمی", "company": "Apa", "price": 2560.00},
        {"code": "2034", "name": "بلاك اوركيد", "company": "Apa", "price": 2865.00},
        {"code": "1047", "name": "بلاك لكسيز XS", "company": "Apa", "price": 2430.00},
        {"code": "1086", "name": "بلو جينز", "company": "Apa", "price": 2435.00},
        {"code": "4062", "name": "بلو شانيل", "company": "Apa", "price": 7875.00},
        {"code": "2035", "name": "بينك شوجر", "company": "Apa", "price": 2145.00},
        {"code": "2950A", "name": "بينك شوجر", "company": "Apa", "price": 2950.00},
        {"code": "1105", "name": "توم فورد اکستریم", "company": "Apa", "price": 2810.00},
        {"code": "4070", "name": "تومي هيل", "company": "Apa", "price": 3665.00},
        {"code": "6015", "name": "جود جيرل", "company": "Apa", "price": 3960.00},
        {"code": "2051", "name": "جادور", "company": "Apa", "price": 2410.00},
        {"code": "1019A", "name": "جوب", "company": "Apa", "price": 2430.00},
    ]
    
    with app.app_context():
        try:
            # إنشاء أو الحصول على الموردين الجدد
            companies = set([item["company"] for item in perfumes_data])
            suppliers = {}
            
            for company_name in companies:
                supplier = Supplier.query.filter_by(name=company_name).first()
                if not supplier:
                    supplier = Supplier(
                        name=company_name,
                        contact_person="غير محدد",
                        phone="غير محدد",
                        address="غير محدد"
                    )
                    db.session.add(supplier)
                    db.session.flush()
                suppliers[company_name] = supplier
            
            # إضافة العطور
            added_count = 0
            updated_count = 0
            
            for perfume_data in perfumes_data:
                # فحص إذا كان العطر موجود بالفعل (بناءً على الكود)
                existing_perfume = Perfume.query.filter_by(sku=perfume_data["code"]).first()
                
                if existing_perfume:
                    # تحديث البيانات الموجودة
                    existing_perfume.name = perfume_data["name"]
                    existing_perfume.company = perfume_data["company"]
                    existing_perfume.price = perfume_data["price"]
                    existing_perfume.price_per_kg = perfume_data["price"]
                    existing_perfume.is_sold_by_weight = True
                    existing_perfume.supplier = suppliers[perfume_data["company"]]
                    existing_perfume.updated_at = datetime.now()
                    updated_count += 1
                    print(f"تم تحديث: {perfume_data['name']} - {perfume_data['company']}")
                else:
                    # إضافة عطر جديد
                    new_perfume = Perfume(
                        name=perfume_data["name"],
                        company=perfume_data["company"],
                        price=perfume_data["price"],
                        price_per_kg=perfume_data["price"],
                        is_sold_by_weight=True,
                        weight_unit='gram',
                        quantity=999999,  # كمية مفتوحة
                        min_quantity=0,  # بدون حد أدنى
                        sku=perfume_data["code"],
                        supplier=suppliers[perfume_data["company"]],
                        is_active=True
                    )
                    db.session.add(new_perfume)
                    added_count += 1
                    print(f"تم إضافة: {perfume_data['name']} - {perfume_data['company']}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n✅ تم الانتهاء بنجاح!")
            print(f"📦 عدد العطور المضافة: {added_count}")
            print(f"🔄 عدد العطور المحدثة: {updated_count}")
            print(f"📊 إجمالي العطور المعالجة: {len(perfumes_data)}")
            print(f"🏢 عدد الموردين الجدد: {len(companies)}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء استيراد العطور من الموردين الجدد...")
    print("=" * 70)
    
    success = import_new_suppliers()
    
    if success:
        print("\n🎉 تم استيراد البيانات بنجاح!")
        print("🌟 تم إضافة موردين جدد للنظام!")
        print("يمكنك الآن تشغيل النظام ومراجعة جميع العطور.")
    else:
        print("\n💥 فشل في استيراد البيانات!")
        print("يرجى مراجعة الأخطاء أعلاه.")
