/* ملف CSS خاص بالطباعة */

@media print {
    /* إعدادات عامة للطباعة */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    @page {
        margin: 1cm;
        size: A4;
    }
    
    body {
        background: white !important;
        color: black !important;
        font-size: 12pt;
        line-height: 1.4;
    }
    
    /* إخفاء العناصر غير المطلوبة */
    .d-print-none,
    .navbar,
    .btn,
    button,
    .alert,
    .breadcrumb,
    .pagination {
        display: none !important;
    }
    
    /* تحسين الكروت للطباعة */
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .card-header {
        background: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6 !important;
        padding: 0.75rem 1rem;
        font-weight: bold;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* تحسين الجداول للطباعة */
    .table {
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 1rem;
    }
    
    .table th,
    .table td {
        border: 1px solid #dee2e6 !important;
        padding: 0.5rem !important;
        text-align: right;
        vertical-align: top;
    }
    
    .table thead th {
        background: #f8f9fa !important;
        font-weight: bold;
        color: black !important;
    }
    
    .table tbody tr:nth-child(even) {
        background: #f8f9fa !important;
    }
    
    /* تحسين النصوص للطباعة */
    h1, h2, h3, h4, h5, h6 {
        color: black !important;
        page-break-after: avoid;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }
    
    p {
        margin-bottom: 0.5rem;
        orphans: 3;
        widows: 3;
    }
    
    /* تحسين الروابط للطباعة */
    a {
        color: black !important;
        text-decoration: none !important;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
    
    /* تحسين الصور للطباعة */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
    
    /* تحسين الفواتير للطباعة */
    .invoice-header {
        background: #f8f9fa !important;
        color: black !important;
        text-align: center;
        padding: 1rem;
        border: 2px solid #dee2e6 !important;
        margin-bottom: 1rem;
    }
    
    .invoice-header h4 {
        margin: 0;
        font-size: 18pt;
        font-weight: bold;
    }
    
    .invoice-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        background: #f8f9fa !important;
    }
    
    .invoice-info div {
        flex: 1;
    }
    
    .invoice-total {
        font-weight: bold !important;
        font-size: 14pt;
        background: #f8f9fa !important;
        padding: 0.5rem;
        text-align: center;
        border: 2px solid #dee2e6 !important;
        margin-top: 1rem;
    }
    
    /* تحسين الأرقام والعملة */
    .currency {
        font-weight: bold;
    }
    
    .currency:after {
        content: " ج.م";
    }
    
    /* تحسين التواريخ */
    .date {
        font-family: monospace;
        direction: ltr;
        text-align: left;
    }
    
    /* تحسين الشارات للطباعة */
    .badge {
        border: 1px solid #dee2e6 !important;
        background: white !important;
        color: black !important;
        padding: 0.25rem 0.5rem;
        font-weight: normal;
    }
    
    /* تحسين التنبيهات للطباعة */
    .alert {
        border: 1px solid #dee2e6 !important;
        background: #f8f9fa !important;
        color: black !important;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    /* تحسين القوائم */
    ul, ol {
        margin-bottom: 1rem;
        padding-right: 1.5rem;
    }
    
    li {
        margin-bottom: 0.25rem;
    }
    
    /* تحسين الفواصل */
    hr {
        border: none;
        border-top: 1px solid #dee2e6 !important;
        margin: 1rem 0;
    }
    
    /* تحسين المحاذاة للعربية */
    .text-right {
        text-align: right !important;
    }
    
    .text-left {
        text-align: left !important;
    }
    
    .text-center {
        text-align: center !important;
    }
    
    /* تحسين الأعمدة للطباعة */
    .row {
        display: flex !important;
        flex-wrap: wrap !important;
        margin: 0 -0.5rem;
    }
    
    .col,
    .col-md-6,
    .col-md-4,
    .col-md-3 {
        flex: 1;
        padding: 0 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    /* تحسين الحاويات */
    .container {
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    /* تحسين المسافات */
    .mb-1 { margin-bottom: 0.25rem !important; }
    .mb-2 { margin-bottom: 0.5rem !important; }
    .mb-3 { margin-bottom: 1rem !important; }
    .mb-4 { margin-bottom: 1.5rem !important; }
    .mb-5 { margin-bottom: 3rem !important; }
    
    .mt-1 { margin-top: 0.25rem !important; }
    .mt-2 { margin-top: 0.5rem !important; }
    .mt-3 { margin-top: 1rem !important; }
    .mt-4 { margin-top: 1.5rem !important; }
    .mt-5 { margin-top: 3rem !important; }
    
    /* تحسين الخطوط */
    .fw-bold {
        font-weight: bold !important;
    }
    
    .fw-normal {
        font-weight: normal !important;
    }
    
    .fs-1 { font-size: 2rem !important; }
    .fs-2 { font-size: 1.75rem !important; }
    .fs-3 { font-size: 1.5rem !important; }
    .fs-4 { font-size: 1.25rem !important; }
    .fs-5 { font-size: 1.1rem !important; }
    .fs-6 { font-size: 1rem !important; }
    
    /* تحسين الألوان للطباعة */
    .text-primary,
    .text-success,
    .text-danger,
    .text-warning,
    .text-info {
        color: black !important;
    }
    
    .text-muted {
        color: #666 !important;
    }
    
    /* تحسين الحدود */
    .border {
        border: 1px solid #dee2e6 !important;
    }
    
    .border-top {
        border-top: 1px solid #dee2e6 !important;
    }
    
    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    /* تحسين الخلفيات */
    .bg-light,
    .bg-primary,
    .bg-success,
    .bg-danger,
    .bg-warning,
    .bg-info {
        background: #f8f9fa !important;
        color: black !important;
    }
    
    /* تحسين فواصل الصفحات */
    .page-break {
        page-break-before: always;
    }
    
    .no-page-break {
        page-break-inside: avoid;
    }
    
    /* تحسين العناوين */
    .print-title {
        font-size: 20pt;
        font-weight: bold;
        text-align: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #dee2e6;
    }
    
    /* تحسين المعلومات الإضافية */
    .print-info {
        font-size: 10pt;
        color: #666;
        text-align: center;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
    
    /* تحسين الأرقام المتسلسلة */
    .print-page-number:after {
        content: counter(page);
    }
    
    /* تحسين التوقيعات */
    .signature-area {
        margin-top: 3rem;
        display: flex;
        justify-content: space-between;
    }
    
    .signature-box {
        width: 200px;
        height: 60px;
        border-bottom: 1px solid #dee2e6;
        text-align: center;
        padding-top: 70px;
        font-size: 10pt;
    }
}
