#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتحديث جميع العطور لتصبح بكمية مفتوحة (بدون قيود)
"""

import sys
import os
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret-key-goes-here'
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data/store.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# استيراد النماذج وتهيئة قاعدة البيانات
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
from backend.models import db, Perfume
db.init_app(app)

def update_to_open_stock():
    """تحديث جميع العطور لتصبح بكمية مفتوحة"""
    
    with app.app_context():
        try:
            print("🔄 بدء تحديث العطور لنظام الكمية المفتوحة...")
            
            # الحصول على جميع العطور
            all_perfumes = Perfume.query.all()
            
            updated_count = 0
            
            for perfume in all_perfumes:
                # تحديث الكمية لتصبح مفتوحة
                if perfume.is_sold_by_weight:
                    # للعطور التي تباع بالوزن - 999.999 كيلو = 999,999 جرام
                    perfume.quantity = 999999
                else:
                    # للعطور التي تباع بالقطعة
                    perfume.quantity = 999999
                
                # تحديث الحد الأدنى ليصبح 0 (بدون تنبيهات)
                perfume.min_quantity = 0
                
                updated_count += 1
                print(f"✅ تم تحديث: {perfume.name} - {perfume.company}")
            
            # حفظ التغييرات
            db.session.commit()
            
            print(f"\n🎉 تم التحديث بنجاح!")
            print(f"📦 عدد العطور المحدثة: {updated_count}")
            print(f"🔓 النظام أصبح مفتوحاً - يمكن البيع بأي كمية")
            print(f"🚫 تم إلغاء جميع تنبيهات المخزون المنخفض")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ حدث خطأ: {str(e)}")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 تحديث النظام ليصبح مفتوح الكمية...")
    print("=" * 50)
    
    success = update_to_open_stock()
    
    if success:
        print("\n✅ تم التحديث بنجاح!")
        print("🎯 المزايا الجديدة:")
        print("   - بيع بأي كمية بدون قيود")
        print("   - لا توجد تنبيهات مخزون منخفض")
        print("   - نظام مرن ومفتوح")
        print("   - سهولة في الاستخدام")
    else:
        print("\n❌ فشل في التحديث!")
        print("يرجى مراجعة الأخطاء أعلاه.")
