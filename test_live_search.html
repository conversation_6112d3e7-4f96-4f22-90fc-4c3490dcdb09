<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث المباشر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-center">اختبار البحث المباشر</h3>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row">
                            <div class="col-md-8 mb-2">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" name="q" class="form-control"
                                           placeholder="ابدأ الكتابة لرؤية البحث المباشر..."
                                           value="">
                                </div>
                            </div>
                            <div class="col-md-2 mb-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search"></i> بحث
                                </button>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="#" class="btn btn-outline-secondary w-100">
                                    <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                                </a>
                            </div>
                        </form>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h5>ميزات البحث المباشر الجديدة:</h5>
                                <ul>
                                    <li>البحث يبدأ من أول حرف تكتبه</li>
                                    <li>تأخير 300 مللي ثانية لتحسين الأداء</li>
                                    <li>البحث التلقائي عند مسح النص</li>
                                    <li>يعمل في جميع صفحات البحث (المنتجات، الفواتير، العملاء، الموردين)</li>
                                </ul>
                            </div>
                            
                            <div id="search-status" class="alert alert-secondary">
                                <strong>حالة البحث:</strong> <span id="status-text">في انتظار الكتابة...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البحث المباشر مع عرض الحالة
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="q"]');
            const statusText = document.getElementById('status-text');
            
            if (searchInput) {
                let timeout;
                
                searchInput.addEventListener('input', function() {
                    clearTimeout(timeout);
                    
                    if (this.value.length === 0) {
                        statusText.textContent = 'في انتظار الكتابة...';
                        return;
                    }
                    
                    statusText.textContent = `جاري البحث عن: "${this.value}"...`;
                    
                    timeout = setTimeout(() => {
                        // البحث يبدأ من أول حرف أو عند مسح النص
                        if (this.value.length >= 1 || this.value.length === 0) {
                            statusText.textContent = `تم إرسال البحث عن: "${this.value}"`;
                            // في التطبيق الحقيقي، سيتم إرسال النموذج هنا
                            // this.form.submit();
                            
                            // محاكاة إرسال النموذج للاختبار
                            setTimeout(() => {
                                statusText.textContent = `نتائج البحث عن: "${this.value}" (محاكاة)`;
                            }, 500);
                        }
                    }, 300);
                });
                
                // إضافة تأثير بصري عند التركيز
                searchInput.addEventListener('focus', function() {
                    this.parentNode.style.boxShadow = '0 0 0 0.2rem rgba(0,123,255,.25)';
                });
                
                searchInput.addEventListener('blur', function() {
                    this.parentNode.style.boxShadow = 'none';
                });
            }
        });
    </script>
</body>
</html>
