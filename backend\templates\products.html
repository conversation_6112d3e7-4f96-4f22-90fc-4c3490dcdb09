<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العطور - نظام إدارة العطور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-hover tbody tr:hover { background-color: #f8f9fa; }
        .low-stock { background-color: #f8d7da !important; }
        .action-buttons .btn { margin: 0 2px; }
    </style>
</head>
<body class="bg-light">
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow">
  <div class="container-fluid">
    <a href="/" class="navbar-brand">
        <i class="bi bi-shop"></i> نظام إدارة العطور
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
            <li class="nav-item">
                <a class="nav-link" href="/"><i class="bi bi-house"></i> الرئيسية</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="/products"><i class="bi bi-box"></i> العطور</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices/add"><i class="bi bi-plus-circle"></i> فاتورة جديدة</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/invoices"><i class="bi bi-receipt"></i> الفواتير</a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="bi bi-gear"></i> إدارة
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/suppliers"><i class="bi bi-truck"></i> الموردين</a></li>
                    <li><a class="dropdown-item" href="/customers"><i class="bi bi-people"></i> العملاء</a></li>
                    <li><a class="dropdown-item" href="/stock"><i class="bi bi-boxes"></i> المخزون</a></li>
                    <li><a class="dropdown-item" href="/reports"><i class="bi bi-graph-up"></i> التقارير</a></li>
                </ul>
            </li>
        </ul>
        <div class="d-flex">
            <a href="/logout" class="btn btn-outline-light">
                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
            </a>
        </div>
    </div>
  </div>
</nav>
<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="bi bi-box"></i> إدارة العطور</h3>
    <div>
        <a href="/products/add" class="btn btn-success me-2">
            <i class="bi bi-plus-circle"></i> إضافة عطر جديد
        </a>
        <a href="/products/export" class="btn btn-info">
            <i class="bi bi-download"></i> تصدير Excel
        </a>
    </div>
  </div>
  <div class="card shadow mb-4">
    <div class="card-body">
      <form class="row" method="get">
        <div class="col-md-8 mb-2">
          <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" name="q" class="form-control" placeholder="بحث بالاسم أو الشركة" value="{{ request.args.get('q', '') }}">
          </div>
        </div>
        <div class="col-md-2 mb-2">
          <button type="submit" class="btn btn-primary w-100">
            <i class="bi bi-search"></i> بحث
          </button>
        </div>
        <div class="col-md-2 mb-2">
          <a href="/products" class="btn btn-outline-secondary w-100">
            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
          </a>
        </div>
      </form>
    </div>
  </div>

  <!-- عداد النتائج -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="alert alert-info d-flex align-items-center">
        <i class="bi bi-info-circle me-2"></i>
        {% if search_query %}
          <span>تم العثور على <strong>{{ results_count }}</strong> نتيجة للبحث عن "<strong>{{ search_query }}</strong>"</span>
        {% else %}
          <span>إجمالي العطور: <strong>{{ results_count }}</strong> عطر</span>
        {% endif %}
      </div>
    </div>
  </div>

  <div class="table-responsive">
    <table class="table table-bordered table-hover bg-white">
      <thead class="table-light">
        <tr>
          <th>الاسم</th>
          <th>الشركة</th>
          <th>السعر</th>
          <th>الكمية</th>
          <th>الحد الأدنى</th>
          <th>إجراءات</th>
        </tr>
      </thead>
      <tbody>
        {% for perfume in perfumes %}
        <tr>
          <td>
            <strong>{{ perfume.name }}</strong>
            {% if perfume.sku %}
            <small class="text-muted d-block">كود: {{ perfume.sku }}</small>
            {% endif %}
          </td>
          <td>{{ perfume.company }}</td>
          <td>
            <span class="fw-bold text-success">
              {% if perfume.is_sold_by_weight %}
                {{ "{:,.2f}".format(perfume.price_per_kg) }} ج.م/كيلو
                <small class="text-muted d-block">{{ "{:.3f}".format(perfume.price_per_kg / 1000) }} ج.م/جرام</small>
              {% else %}
                {{ "{:,.2f}".format(perfume.price) }} ج.م/قطعة
              {% endif %}
            </span>
          </td>
          <td>
            <span class="badge bg-success">
              {% if perfume.is_sold_by_weight %}
                {{ "{:,.0f}".format(perfume.quantity) }} جرام
              {% else %}
                {{ perfume.quantity }} قطعة
              {% endif %}
            </span>
          </td>
          <td>
            {% if perfume.min_quantity > 0 %}
              {{ perfume.min_quantity }}
            {% else %}
              <span class="text-muted">مفتوح</span>
            {% endif %}
          </td>
          <td class="action-buttons">
            <a href="/products/edit/{{ perfume.id }}" class="btn btn-sm btn-warning" title="تعديل">
              <i class="bi bi-pencil"></i>
            </a>
            <a href="/products/delete/{{ perfume.id }}" class="btn btn-sm btn-danger"
               onclick="return confirm('هل أنت متأكد من حذف {{ perfume.name }}؟');" title="حذف">
              <i class="bi bi-trash"></i>
            </a>
          </td>
        </tr>
        {% else %}
        <tr><td colspan="6" class="text-center text-muted py-4">
          <i class="bi bi-box display-6 d-block mb-2"></i>
          لا توجد عطور مسجلة
        </td></tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>